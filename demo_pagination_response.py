#!/usr/bin/env python3
"""
Demo Pagination Response Format for So Chi Tiet Cong No Nhieu Khach Hang

Shows the standard DRF pagination format like Bang Can Doi Phat Sinh Cong No.
"""

import j<PERSON>

def demo_pagination_response():
    """Demo the standard DRF pagination response format."""
    print("🎯 Standard DRF Pagination Response Format")
    print("🚀 So Chi Tiet Cong No Nhieu Khach Hang")
    print("=" * 60)
    
    # Standard DRF pagination response (like Bang Can Doi)
    paginated_response = {
        "count": 25,  # Total number of records
        "next": "http://localhost:8003/api/entities/my-company/erp/ban-hang/cong-no-khach-hang/so-chi-tiet-cong-no-nhieu-khach-hang/?page=2",
        "previous": None,  # First page
        "results": [
            # ROW 1: SUMMARY ROW (stt=0, always first)
            {
                "stt": 0,
                "tk": "",
                "ma_kh": "",
                "ten_kh": "Tổng cộng",
                "no_dk": 5000000.0,
                "co_dk": 1000000.0,
                "ps_no": 1500000.0,
                "ps_co": 500000.0,
                "no_ck": 6000000.0,
                "co_ck": 0.0,
                "bac_ct": 0
            },
            # ROW 2: CUSTOMER 1 (stt=1)
            {
                "stt": 1,
                "tk": "131",
                "ma_kh": "KH001",
                "ten_kh": "Công ty ABC",
                "no_dk": 1000000.0,
                "co_dk": 0.0,
                "ps_no": 500000.0,
                "ps_co": 200000.0,
                "no_ck": 1300000.0,
                "co_ck": 0.0,
                "bac_ct": 1
            },
            # ROW 3: CUSTOMER 2 (stt=2)
            {
                "stt": 2,
                "tk": "131",
                "ma_kh": "KH002",
                "ten_kh": "Công ty XYZ",
                "no_dk": 2000000.0,
                "co_dk": 500000.0,
                "ps_no": 300000.0,
                "ps_co": 100000.0,
                "no_ck": 1700000.0,
                "co_ck": 0.0,
                "bac_ct": 1
            },
            # ... more customers up to page_size limit (20 by default)
        ]
    }
    
    print("📋 Standard DRF Pagination Response:")
    print(json.dumps(paginated_response, indent=2, ensure_ascii=False))
    
    print(f"\n🔍 Response Structure Analysis:")
    print(f"   Format: Standard DRF PageNumberPagination")
    print(f"   Total records: {paginated_response['count']}")
    print(f"   Current page records: {len(paginated_response['results'])}")
    print(f"   Next page: {'Available' if paginated_response['next'] else 'None'}")
    print(f"   Previous page: {'Available' if paginated_response['previous'] else 'None'}")
    print(f"   Summary row: Always first (stt=0)")


def demo_pagination_parameters():
    """Demo pagination parameters."""
    print(f"\n\n📡 Pagination Parameters")
    print("=" * 60)
    
    pagination_examples = [
        {
            "description": "Default pagination (page 1, 20 items)",
            "url": "?page=1&page_size=20",
            "expected_behavior": "Returns first 20 records including summary row"
        },
        {
            "description": "Custom page size (50 items)",
            "url": "?page=1&page_size=50", 
            "expected_behavior": "Returns first 50 records including summary row"
        },
        {
            "description": "Second page",
            "url": "?page=2&page_size=20",
            "expected_behavior": "Returns records 21-40 (NO summary row on page 2+)"
        },
        {
            "description": "Maximum page size",
            "url": "?page=1&page_size=100",
            "expected_behavior": "Returns first 100 records (max allowed)"
        }
    ]
    
    for i, example in enumerate(pagination_examples, 1):
        print(f"\n🧪 Example {i}: {example['description']}")
        print(f"   URL: /api/entities/{{entity_slug}}/erp/ban-hang/cong-no-khach-hang/so-chi-tiet-cong-no-nhieu-khach-hang/{example['url']}")
        print(f"   Behavior: {example['expected_behavior']}")


def demo_curl_with_pagination():
    """Demo cURL requests with pagination."""
    print(f"\n\n📡 cURL Examples with Pagination")
    print("=" * 60)
    
    # Example 1: First page with default pagination
    curl_page1 = '''curl -X POST 'http://localhost:8003/api/entities/{entity_slug}/erp/ban-hang/cong-no-khach-hang/so-chi-tiet-cong-no-nhieu-khach-hang/?page=1&page_size=20' \\
  -u 'admin:password' \\
  -H 'Content-Type: application/json' \\
  -d '{
    "ngay_ct1": "20250101",
    "ngay_ct2": "20251231",
    "tk": "131",
    "ma_kh": "",
    "nh_kh1": "12345678-1234-1234-1234-123456789abc",
    "rg_code": "87654321-4321-4321-4321-cba987654321"
  }'
'''
    
    print("📋 Example 1: First page (includes summary row)")
    print(curl_page1)
    
    # Example 2: Second page
    curl_page2 = '''curl -X POST 'http://localhost:8003/api/entities/{entity_slug}/erp/ban-hang/cong-no-khach-hang/so-chi-tiet-cong-no-nhieu-khach-hang/?page=2&page_size=20' \\
  -u 'admin:password' \\
  -H 'Content-Type: application/json' \\
  -d '{
    "ngay_ct1": "20250101",
    "ngay_ct2": "20251231",
    "tk": "131",
    "ma_kh": ""
  }'
'''
    
    print("\n📋 Example 2: Second page (no summary row)")
    print(curl_page2)


def demo_summary_row_behavior():
    """Demo summary row behavior with pagination."""
    print(f"\n\n📊 Summary Row Behavior with Pagination")
    print("=" * 60)
    
    behaviors = [
        {
            "scenario": "Page 1 (any page size)",
            "summary_row": "✅ INCLUDED",
            "position": "First record (stt=0)",
            "calculation": "Sum of ALL customers (not just current page)"
        },
        {
            "scenario": "Page 2+ (any page size)",
            "summary_row": "❌ NOT INCLUDED",
            "position": "N/A",
            "calculation": "N/A"
        },
        {
            "scenario": "Empty results",
            "summary_row": "✅ INCLUDED",
            "position": "Only record (stt=0)",
            "calculation": "All zeros"
        }
    ]
    
    print("📋 Summary Row Rules:")
    for behavior in behaviors:
        print(f"\n🎯 {behavior['scenario']}:")
        print(f"   Summary row: {behavior['summary_row']}")
        print(f"   Position: {behavior['position']}")
        print(f"   Calculation: {behavior['calculation']}")


def demo_comparison_with_bang_can_doi():
    """Compare with Bang Can Doi Phat Sinh Cong No format."""
    print(f"\n\n🔄 Comparison with Bang Can Doi Phat Sinh Cong No")
    print("=" * 60)
    
    comparison = [
        {
            "aspect": "Response Format",
            "bang_can_doi": "Standard DRF pagination",
            "so_chi_tiet": "Standard DRF pagination",
            "status": "✅ SAME"
        },
        {
            "aspect": "Pagination Class",
            "bang_can_doi": "ERPPagination",
            "so_chi_tiet": "ERPPagination", 
            "status": "✅ SAME"
        },
        {
            "aspect": "Page Size",
            "bang_can_doi": "20 (default), max 100",
            "so_chi_tiet": "20 (default), max 100",
            "status": "✅ SAME"
        },
        {
            "aspect": "Summary Row",
            "bang_can_doi": "No summary row",
            "so_chi_tiet": "Summary row on page 1",
            "status": "🔄 DIFFERENT"
        },
        {
            "aspect": "Request Method",
            "bang_can_doi": "POST with body data",
            "so_chi_tiet": "POST with body data",
            "status": "✅ SAME"
        },
        {
            "aspect": "Authentication",
            "bang_can_doi": "IsAuthenticated",
            "so_chi_tiet": "IsAuthenticated",
            "status": "✅ SAME"
        }
    ]
    
    print("📊 Feature Comparison:")
    for item in comparison:
        print(f"\n{item['status']} {item['aspect']}:")
        print(f"   Bang Can Doi: {item['bang_can_doi']}")
        print(f"   So Chi Tiet: {item['so_chi_tiet']}")


def main():
    """Run the complete pagination demo."""
    demo_pagination_response()
    demo_pagination_parameters()
    demo_curl_with_pagination()
    demo_summary_row_behavior()
    demo_comparison_with_bang_can_doi()
    
    print(f"\n" + "=" * 60)
    print("🎉 PAGINATION IMPLEMENTATION COMPLETE!")
    print("=" * 60)
    print("✅ Standard DRF pagination format")
    print("✅ Compatible with Bang Can Doi pattern")
    print("✅ Summary row logic maintained")
    print("✅ ERPPagination class used")
    print("✅ Proper authentication and validation")
    print("✅ Ready for frontend integration!")


if __name__ == '__main__':
    main()
