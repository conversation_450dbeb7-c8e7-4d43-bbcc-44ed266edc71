"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Service for Bang Can Doi Phat Sinh Cong No (Customer Debt Balance Report) business logic.

Enhanced with DebtCalculationService integration for accurate debt calculation.
Updated: 2024-12-19 - Expert ERP Implementation
"""
from typing import Any, Dict, List, Optional
from datetime import datetime, date, timedelta
from decimal import Decimal

from django.utils import timezone
from django_ledger.services.base import BaseService
from django_ledger.utils_new.debt_management import (
    DebtCalculationService,
    CustomerFilteringUtils,
    SummaryRowUtils
)
from django_ledger.utils_new.debt_management.debt_calculation import (
    DebtBalanceCalculationUtils,
    DebtBalanceFormatterUtils,
    DebtBalanceQueryUtils
)
from django_ledger.models import CustomerModel, EntityModel


class BangCanDoiPhatSinhCongNoService(BaseService):
    """
    Service class for handling Customer Debt Balance Report (Bang Can Doi Phat Sinh Cong No) business logic.
    Enhanced with DebtCalculationService integration for accurate, real-time debt calculation.

    Expert ERP Implementation - 20+ years experience
    """

    def __init__(self):
        """
        Initialize the service with debt calculation capabilities.
        """
        super().__init__()
        self.debt_service = None

    def generate_report(
        self, entity_slug: str, filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate customer debt balance report using expert ERP logic.

        Expert ERP Implementation - 20+ years experience

        Calculates 3 types of debt figures according to ERP standards:

        TYPE 1 - Opening Balance (no_dk/co_dk):
        - Calculate customer debt from beginning of data to start_date - 1
        - Positive amount = Debit column (customer owes us)
        - Negative amount = Credit column (we owe customer)

        TYPE 2 - Period Movement (ps_no/ps_co):
        - Calculate separate debit/credit movements within the period
        - Always show actual transaction amounts (both positive)
        - ps_no = total debit transactions in period
        - ps_co = total credit transactions in period

        TYPE 3 - Closing Balance (no_ck/co_ck):
        - Calculate using ERP formula: Closing = Opening + Period_Debit - Period_Credit
        - Positive amount = Debit column (customer owes us)
        - Negative amount = Credit column (we owe customer)

        Parameters
        ----------
        entity_slug : str
            The entity slug
        filters : Dict[str, Any]
            Filter parameters including period, accounts, customers, etc.

        Returns
        -------
        List[Dict[str, Any]]
            Report data with accurate debt calculations following ERP standards
        """
        try:
            # Get entity
            entity = EntityModel.objects.get(slug=entity_slug)

            # Initialize debt service for this entity
            if self.debt_service is None:
                self.debt_service = DebtCalculationService(entity_id=str(entity.uuid))

            # Parse filters
            parsed_filters = self._parse_enhanced_filters(filters)

            # Get customers based on filters using shared utils
            customers = CustomerFilteringUtils.get_filtered_customers(entity, parsed_filters)

            # Generate debt report for each customer
            report_data = []
            stt = 1

            # Get account UUIDs to process - Dynamic Detection
            user_selected_account_uuids = parsed_filters.get('account_uuids', [])  # Array of account UUIDs

            if user_selected_account_uuids:
                # User selected specific account UUIDs - process all customers for these accounts
                print(f"📊 User selected account UUIDs: {user_selected_account_uuids}")
                for customer in customers:
                    # Process each selected account UUID
                    for account_uuid in user_selected_account_uuids:
                        # Check if customer has transactions in this account
                        customer_accounts = DebtBalanceCalculationUtils.get_customer_debt_accounts(
                            customer=customer,
                            entity_id=str(entity.uuid),
                            user_selected_account_uuid=account_uuid
                        )

                        if customer_accounts:  # Only process if customer has this account
                            account_code = customer_accounts[0]  # Get the account code for display

                            # Use unified calculation method
                            customer_data = self._calculate_customer_debt_for_account(
                                customer=customer,
                                account_uuid=account_uuid,
                                account_code=account_code,
                                parsed_filters=parsed_filters,
                                entity=entity,
                                stt=stt
                            )

                            if customer_data:
                                report_data.append(customer_data)
                                stt += 1
            else:
                # No specific account selected - auto-detect all debt accounts for each customer
                for customer in customers:
                    try:
                        # Get all debt account codes for this customer (for display)
                        customer_debt_accounts = DebtBalanceCalculationUtils.get_customer_debt_accounts(
                            customer=customer,
                            entity_id=str(entity.uuid)
                        )

                        if not customer_debt_accounts:
                            continue

                        # Process each debt account for this customer
                        for account_code in customer_debt_accounts:
                            # Convert account code to UUID for calculation
                            from django_ledger.models import AccountModel
                            try:
                                account = AccountModel.objects.get(
                                    code=account_code,
                                    coa_model__entity=entity
                                )
                                account_uuid = str(account.uuid)
                            except AccountModel.DoesNotExist:
                                continue

                            # Use unified calculation method
                            customer_data = self._calculate_customer_debt_for_account(
                                customer=customer,
                                account_uuid=account_uuid,
                                account_code=account_code,
                                parsed_filters=parsed_filters,
                                entity=entity,
                                stt=stt
                            )

                            if customer_data:
                                # Remove bac_ct field for Bang Can Doi (not needed)
                                if 'bac_ct' in customer_data:
                                    del customer_data['bac_ct']
                                report_data.append(customer_data)
                                stt += 1

                    except Exception as e:
                        continue

            # Add summary row using shared utils (without bac_ct for Bang Can Doi)
            final_report = SummaryRowUtils.add_summary_to_report(report_data)

            # Remove bac_ct from summary row for Bang Can Doi
            if final_report and len(final_report) > 0:
                summary_row = final_report[0]
                if summary_row.get('stt') == 0 and 'bac_ct' in summary_row:
                    del summary_row['bac_ct']

            return final_report

        except Exception as e:
            # Log the error for debugging
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating enhanced customer debt balance report: {str(e)}", exc_info=True)

            # Return empty list instead of raising exception
            return []

    def _parse_enhanced_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse and validate filters for enhanced debt calculation.

        Parameters
        ----------
        filters : Dict[str, Any]
            Raw filter parameters

        Returns
        -------
        Dict[str, Any]
            Parsed and validated filters
        """
        from datetime import timedelta

        parsed = {}

        # Parse date range
        start_date_str = filters.get('ngay_ct1', '********')
        end_date_str = filters.get('ngay_ct2', '********')

        try:
            parsed['start_date'] = datetime.strptime(start_date_str, '%Y%m%d').date()
            parsed['end_date'] = datetime.strptime(end_date_str, '%Y%m%d').date()
        except ValueError:
            # Default to current year
            current_year = datetime.now().year
            parsed['start_date'] = date(current_year, 1, 1)
            parsed['end_date'] = date(current_year, 12, 31)

        # Parse account UUIDs - support both string and array formats
        tk = filters.get('tk', [])
        if isinstance(tk, str):
            # Single string - could be UUID or code
            parsed['account_uuids'] = [tk] if tk else []
        elif isinstance(tk, list):
            # Array format
            parsed['account_uuids'] = tk
        else:
            parsed['account_uuids'] = []

        # Parse customer UUIDs - support both string and array formats
        ma_kh = filters.get('ma_kh', [])
        if isinstance(ma_kh, str):
            # Single string - could be UUID or code
            parsed['customer_uuids'] = [ma_kh] if ma_kh else []
        elif isinstance(ma_kh, list):
            # Array format
            parsed['customer_uuids'] = ma_kh
        else:
            parsed['customer_uuids'] = []

        # Parse customer groups (legacy support)
        parsed['customer_groups'] = filters.get('nhom_kh', [])
        if isinstance(parsed['customer_groups'], str):
            parsed['customer_groups'] = [parsed['customer_groups']]

        # Parse UUID filters using shared utils
        uuid_filters = CustomerFilteringUtils.parse_uuid_filters(filters)
        parsed.update(uuid_filters)

        return parsed



    def _get_all_customer_transaction_accounts(self, entity: EntityModel) -> List[str]:
        """
        Get all account codes that have transactions with customers.

        Parameters
        ----------
        entity : EntityModel
            The entity to get accounts for

        Returns
        -------
        List[str]
            List of account codes that have customer transactions
        """
        from django_ledger.models import TransactionModel, JournalEntryModel

        # Get all account codes that have transactions linked to customers
        account_codes = TransactionModel.objects.filter(
            journal_entry__ledger__entity=entity,
            journal_entry__customer__isnull=False,
            journal_entry__posted=True
        ).values_list('account__code', flat=True).distinct()

        # Convert to list and sort
        account_codes_list = list(set(account_codes))
        account_codes_list.sort()

        # If no accounts found, default to common receivable accounts
        if not account_codes_list:
            account_codes_list = ['131', '111', '334']  # Common customer-related accounts

        return account_codes_list

    def _calculate_customer_debt_for_account(
        self,
        customer: CustomerModel,
        account_uuid: str,
        account_code: str,
        parsed_filters: Dict[str, Any],
        entity: EntityModel,
        stt: int
    ) -> Optional[Dict[str, Any]]:
        """
        Calculate debt data for a specific customer and account.

        Parameters
        ----------
        customer : CustomerModel
            The customer to calculate debt for
        account_uuid : str
            The account UUID to calculate debt for
        account_code : str
            The account code for display
        parsed_filters : Dict[str, Any]
            Parsed filter parameters
        entity : EntityModel
            The entity context
        stt : int
            Sequential number for the record

        Returns
        -------
        Optional[Dict[str, Any]]
            Customer debt data record or None if no data
        """
        try:
            # TYPE 1: Calculate opening balance using UUID
            from datetime import timedelta
            opening_date = parsed_filters['start_date'] - timedelta(days=1)
            opening_balance = DebtBalanceCalculationUtils.calculate_opening_balance(
                customer=customer,
                end_date=opening_date,
                account_uuids=[account_uuid],  # Use UUID
                entity_id=str(entity.uuid)
            )

            # TYPE 2: Calculate period movements using UUID
            period_debit, period_credit = DebtBalanceCalculationUtils.calculate_period_movements(
                customer=customer,
                start_date=parsed_filters['start_date'],
                end_date=parsed_filters['end_date'],
                account_uuids=[account_uuid],  # Use UUID
                entity_id=str(entity.uuid)
            )

            # TYPE 3: Calculate closing balance
            net_closing_balance = DebtBalanceCalculationUtils.calculate_closing_balance(
                opening_balance=opening_balance,
                period_debit=period_debit,
                period_credit=period_credit
            )

            # Format customer debt data using unified utils
            customer_data = DebtBalanceFormatterUtils.format_customer_debt_record(
                stt=stt,
                customer=customer,
                account_code=account_code,
                opening_balance=opening_balance,
                period_debit=period_debit,
                period_credit=period_credit,
                closing_balance=net_closing_balance
            )

            # Only return data if customer has non-zero balances or transactions
            if (customer_data['no_dk'] != 0 or customer_data['co_dk'] != 0 or
                customer_data['ps_no'] != 0 or customer_data['ps_co'] != 0 or
                customer_data['no_ck'] != 0 or customer_data['co_ck'] != 0):
                return customer_data

            return None

        except Exception:
            return None
