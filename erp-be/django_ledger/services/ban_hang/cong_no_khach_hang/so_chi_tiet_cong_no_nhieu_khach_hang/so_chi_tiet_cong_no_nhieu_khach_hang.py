"""
Django Ledger created by <PERSON> <<EMAIL>>.
Copyright© EDMA Group Inc licensed under the GPLv3 Agreement.

Dịch vụ cho Sổ Chi Tiết Công Nợ Nhiều Khách <PERSON>ng - Logic nghiệp vụ báo cáo công nợ.

Nâng cao với báo cáo chi tiết cấp độ giao dịch để phân tích công nợ nhiều khách hàng.
Cập nhật: 2025-01-15 - Triển khai ERP Chuyên nghiệp
"""
from typing import Any, Dict, List
from datetime import datetime, date, timedelta
from decimal import Decimal

from django.utils import timezone
from django_ledger.services.base import BaseService
from django_ledger.models import (
    CustomerModel,
    EntityModel,
    TransactionModel,
    JournalEntryModel,
    HoaDonBanHangModel,
    PhieuThuModel,
    PhieuChiModel
)
from django_ledger.utils_new.debt_management import DebtCalculationService
from django_ledger.utils_new.debt_management.debt_calculation import (
    DebtBalanceCalculationUtils,
    DebtBalanceFormatterUtils,
    DebtBalanceQueryUtils
)
from django_ledger.utils_new.debt_management.debt_detail_utils import DebtDetailUtils


class SoChiTietCongNoNhieuKhachHangService(BaseService):
    """
    Lớp dịch vụ xử lý logic nghiệp vụ Sổ Chi Tiết Công Nợ Nhiều Khách Hàng.

    Báo cáo này hiển thị chi tiết giao dịch cho nhiều khách hàng,
    cung cấp lịch sử giao dịch từng dòng với số dư lũy kế.

    Khác biệt so với Bảng Cân Đối Phát Sinh Công Nợ:
    - Tài khoản (tk): BẮT BUỘC, chỉ cho phép 1 tài khoản duy nhất
    - Khách hàng (ma_kh): Tùy chọn, rỗng = tất cả khách hàng, có giá trị = chỉ khách hàng đó
    - Logic tính toán: Giống hệt Bảng Cân Đối (type 1, 2, 3)

    Triển khai ERP Chuyên nghiệp - 20+ năm kinh nghiệm
    """

    def __init__(self):
        """
        Khởi tạo dịch vụ với khả năng tính toán công nợ.
        """
        super().__init__()
        self.debt_service = None

    def generate_report(
        self, entity_slug: str, filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate customer debt detail report showing individual transactions.

        DIFFERENT from Bang Can Doi: Returns transaction details instead of balance summary.
        Same structure and calculation logic as Bang Can Doi for consistency.
        """
        try:
            # Get entity - same as Bang Can Doi
            entity = EntityModel.objects.get(slug=entity_slug)

            # Initialize debt service for this entity - same as Bang Can Doi
            if self.debt_service is None:
                self.debt_service = DebtCalculationService(entity_id=str(entity.uuid))

            # Parse filters - same as Bang Can Doi
            parsed_filters = self._parse_enhanced_filters(filters)

            # Get customers based on filters - same as Bang Can Doi
            customers = self._get_filtered_customers(entity, parsed_filters)

            # Get transaction details - DIFFERENT from Bang Can Doi
            transactions = self._get_filtered_transactions(entity, parsed_filters)

            # Build report data with transaction details - DIFFERENT from Bang Can Doi
            report_data = []
            stt = 1

            for transaction in transactions:
                try:
                    transaction_data = self._format_transaction_record(
                        stt=stt,
                        transaction=transaction,
                        parsed_filters=parsed_filters
                    )

                    if transaction_data:
                        report_data.append(transaction_data)
                        stt += 1

                        # Add product details if requested
                        if parsed_filters.get('ct_vt', False):
                            stt = self._add_product_details(
                                transaction, transaction_data, report_data, stt, parsed_filters
                            )

                except Exception:
                    continue

            # Calculate running balance if requested
            if parsed_filters.get('so_du', False):
                self._calculate_running_balance(report_data, parsed_filters)

            return report_data

        except Exception as e:
            # Log the error for debugging - same as Bang Can Doi
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating customer debt detail report: {str(e)}", exc_info=True)

            # Return empty list instead of raising exception - same as Bang Can Doi
            return []

    def _parse_enhanced_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse and validate filters for enhanced debt calculation.
        Same as Bang Can Doi but modified for single account requirement.
        """
        from datetime import timedelta

        parsed = {}

        # Parse date range - same as Bang Can Doi
        start_date_str = filters.get('ngay_ct1', '********')
        end_date_str = filters.get('ngay_ct2', '********')

        try:
            parsed['start_date'] = datetime.strptime(start_date_str, '%Y%m%d').date()
            parsed['end_date'] = datetime.strptime(end_date_str, '%Y%m%d').date()
        except ValueError:
            # Default to current year
            current_year = datetime.now().year
            parsed['start_date'] = date(current_year, 1, 1)
            parsed['end_date'] = date(current_year, 12, 31)

        # Parse account - DIFFERENT from Bang Can Doi: single account required
        tk = filters.get('tk', '')
        if not tk:
            raise ValueError("Account code (tk) is required and must not be empty")
        parsed['tk'] = tk

        # Parse customer - DIFFERENT from Bang Can Doi: single customer code or empty
        ma_kh = filters.get('ma_kh', '')
        if isinstance(ma_kh, str):
            parsed['customer_code'] = ma_kh if ma_kh else ''
        else:
            parsed['customer_code'] = ''

        # Parse customer groups - same as Bang Can Doi
        parsed['customer_groups'] = filters.get('nhom_kh', [])
        if isinstance(parsed['customer_groups'], str):
            parsed['customer_groups'] = [parsed['customer_groups']]

        return parsed

    def _get_filtered_customers(self, entity: EntityModel, filters: Dict[str, Any]) -> List[CustomerModel]:
        """
        Get customers based on filter criteria.
        Same as Bang Can Doi but modified for single customer filtering.
        """
        queryset = CustomerModel.objects.filter(entity=entity, hidden=False)

        # Filter by single customer code - DIFFERENT from Bang Can Doi
        customer_code = filters.get('customer_code', '').strip()
        if customer_code:
            # If customer code is specified, return only that specific customer
            queryset = queryset.filter(customer_number=customer_code)

        # Filter by customer groups - same as Bang Can Doi
        if filters.get('customer_groups'):
            from django.db.models import Q
            group_filter = Q()
            for group in filters['customer_groups']:
                group_filter |= (
                    Q(nh_kh1__icontains=group) |
                    Q(nh_kh2__icontains=group) |
                    Q(nh_kh3__icontains=group)
                )
            queryset = queryset.filter(group_filter)

        # If no specific filters, return all customers (backward compatibility) - same as Bang Can Doi
        return list(queryset.order_by('customer_number'))

    def _get_filtered_transactions(self, entity: EntityModel, filters: Dict[str, Any]) -> List[TransactionModel]:
        """
        Get transactions based on filter criteria for customers and single account.
        """
        # Get filtered customers first
        customers = self._get_filtered_customers(entity, filters)

        if not customers:
            return []

        # Base queryset for customer-related transactions
        queryset = TransactionModel.objects.filter(
            journal_entry__ledger__entity=entity,
            journal_entry__customer__in=customers,
            journal_entry__posted=True,
            journal_entry__timestamp__date__gte=filters['start_date'],
            journal_entry__timestamp__date__lte=filters['end_date'],
            amount__gt=0  # Only include transactions with amount > 0
        ).select_related(
            'journal_entry',
            'journal_entry__customer',
            'account'
        ).order_by(
            'journal_entry__customer__customer_number',  # Group by customer
            'journal_entry__timestamp',
            'journal_entry__uuid',
            'uuid'
        )

        # Filter by exact account code (single account only)
        queryset = queryset.filter(account__code=filters['tk'])

        return list(queryset)

    def _format_transaction_record(
        self, stt: int, transaction: TransactionModel, parsed_filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Format a single transaction record for the report.
        Modified to include customer information for multiple customers report.

        Parameters
        ----------
        stt : int
            Sequential number
        transaction : TransactionModel
            The transaction to format
        parsed_filters : Dict[str, Any]
            Parsed filter parameters

        Returns
        -------
        Dict[str, Any]
            Formatted transaction record with customer data
        """
        journal_entry = transaction.journal_entry
        customer = journal_entry.customer
        ledger = journal_entry.ledger

        # Get source document (hóa đơn/phiếu) from ledger relationship
        source_document = DebtDetailUtils.get_source_document_from_ledger(ledger)

        # Calculate tk_du (corresponding account) from journal entry transactions
        tk_du, ps_no, ps_co = DebtDetailUtils.calculate_corresponding_account_and_amounts(
            transaction, journal_entry
        )

        # Map data from source document if available
        document_data = DebtDetailUtils.extract_document_data(source_document)

        # Base transaction record with customer information
        record = {
            'stt': stt,
            'tk': transaction.account.code if transaction.account else '',
            'ma_kh': customer.customer_number if customer else '',  # Customer code
            'ten_kh': customer.customer_name if customer else '',   # Customer name
            'no_dk': 0.0,    # Opening debit (will be calculated separately)
            'co_dk': 0.0,    # Opening credit (will be calculated separately)
            'ps_no': ps_no,  # Period debit movement
            'ps_co': ps_co,  # Period credit movement
            'no_ck': 0.0,    # Closing debit (will be calculated separately)
            'co_ck': 0.0,    # Closing credit (will be calculated separately)
            'bac_ct': 1,     # Document level (default 1)
            # Additional fields for compatibility
            'so_ct0': document_data.get('so_ct0', ''),
            'ngay_ct0': document_data.get('ngay_ct0', ''),
            'xorder': stt,
            'line': stt,
            'id': str(transaction.uuid),
            'unit_id': document_data.get('unit_id', ''),
            'ma_ct': document_data.get('ma_ct', ''),
            'ngay_ct': document_data.get('ngay_ct', ''),
            'so_ct': document_data.get('so_ct', ''),
            'tk_du': tk_du,
            'dien_giai': document_data.get('dien_giai', journal_entry.description or transaction.description or ''),
            'ma_bp': parsed_filters.get('ma_bp', ''),
            'ma_vv': parsed_filters.get('ma_vv', ''),
            'ma_unit': parsed_filters.get('ma_unit', ''),
        }

        # Add product detail columns if ct_vt is requested
        if parsed_filters.get('ct_vt', 0):
            record.update({
                'so_luong': 0.0,  # Default empty for transaction records
                'gia2': 0.0,      # Default empty for transaction records
                'tien2': 0.0,     # Default empty for transaction records
            })

        # Add running balance columns if so_du is requested
        if parsed_filters.get('so_du', 0):
            record.update({
                'du_no': 0.0,  # Will be calculated in main loop
                'du_co': 0.0,  # Will be calculated in main loop
            })

        return record

    def _add_product_details(
        self,
        transaction: TransactionModel,
        transaction_data: Dict[str, Any],
        report_data: List[Dict[str, Any]],
        stt: int,
        parsed_filters: Dict[str, Any]
    ) -> int:
        """
        Add product details for a transaction if it's a sales invoice.

        Returns updated stt counter.
        """
        try:
            ledger = transaction.journal_entry.ledger
            source_document = DebtDetailUtils.get_source_document_from_ledger(ledger)

            if source_document and hasattr(source_document, 'chi_tiet_hoa_don_ban_hang_set'):
                product_details = self._get_invoice_details(source_document)

                for product_detail in product_details:
                    product_detail_record = self._format_product_detail_record(
                        stt=stt,
                        product_detail=product_detail,
                        base_transaction_record=transaction_data,
                        parsed_filters=parsed_filters
                    )

                    if product_detail_record:
                        report_data.append(product_detail_record)
                        stt += 1
        except Exception:
            pass

        return stt

    def _get_invoice_details(self, source_document) -> List[Dict[str, Any]]:
        """
        Get product details from invoice (hóa đơn bán hàng).

        Parameters
        ----------
        source_document : HoaDonBanHangModel
            The source invoice document

        Returns
        -------
        List[Dict[str, Any]]
            List of product detail records
        """
        if not source_document:
            return []

        # Check if this is a sales invoice with details
        if hasattr(source_document, 'chi_tiet_hoa_don_ban_hang_set'):
            try:
                details = source_document.chi_tiet_hoa_don_ban_hang_set.all().select_related(
                    'vat_tu'
                ).order_by('id')

                detail_records = []
                for detail in details:
                    # Format product detail record
                    detail_record = {
                        'ma_vt': detail.vat_tu.ma_vt if detail.vat_tu else '',
                        'ten_vt0': detail.vat_tu.ten_vt0 if detail.vat_tu else '',
                        'so_luong': float(detail.so_luong) if detail.so_luong else 0.0,
                        'gia2': float(detail.gia2) if detail.gia2 else 0.0,
                        'tien2': float(detail.tien2) if detail.tien2 else 0.0,
                    }
                    detail_records.append(detail_record)

                return detail_records

            except Exception:
                return []

        return []

    def _format_product_detail_record(
        self,
        stt: int,
        product_detail: Dict[str, Any],
        base_transaction_record: Dict[str, Any],
        parsed_filters: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Format a product detail record based on transaction record template.

        Parameters
        ----------
        stt : int
            Sequential number
        product_detail : Dict[str, Any]
            Product detail data from invoice
        base_transaction_record : Dict[str, Any]
            Base transaction record to copy fields from
        parsed_filters : Dict[str, Any]
            Parsed filter parameters

        Returns
        -------
        Dict[str, Any]
            Formatted product detail record
        """
        # Copy base transaction record and modify specific fields
        detail_record = base_transaction_record.copy()

        # Update specific fields for product detail
        detail_record.update({
            'stt': stt,
            'tk': '',  # Empty for product detail
            'tk_du': '',  # Empty for product detail
            'ps_no': 0.0,  # Empty for product detail
            'ps_co': 0.0,  # Empty for product detail
            'dien_giai': f"{product_detail['ma_vt']} - {product_detail['ten_vt0']}",
            'line': stt,
            # Add product detail columns
            'so_luong': product_detail['so_luong'],
            'gia2': product_detail['gia2'],
            'tien2': product_detail['tien2'],
        })

        return detail_record

    def _calculate_running_balance(self, report_data: List[Dict[str, Any]], parsed_filters: Dict[str, Any]) -> None:
        """
        Tính số dư lũy kế cho các bản ghi giao dịch khi so_du=1.
        Modified for multiple customers - maintains separate running balance per customer.

        Parameters
        ----------
        report_data : List[Dict[str, Any]]
            Dữ liệu báo cáo chứa các bản ghi tổng hợp và giao dịch
        parsed_filters : Dict[str, Any]
            Tham số bộ lọc đã phân tích
        """
        if len(report_data) < 3:
            return  # Cần ít nhất 3 dòng tổng hợp

        # Dictionary to track running balance per customer
        customer_balances = {}

        # Process transaction records (starting from row 4, index 3)
        for i in range(3, len(report_data)):
            record = report_data[i]

            # Skip product detail rows (empty tk field)
            if not record.get('tk', '').strip():
                # For product detail rows, copy balance from parent transaction
                if i > 3:
                    prev_record = report_data[i-1]
                    record['du_no'] = prev_record.get('du_no', 0.0)
                    record['du_co'] = prev_record.get('du_co', 0.0)
                continue

            # This is a transaction record - calculate running balance per customer
            customer_code = record.get('ma_kh', '')
            ps_no = float(record.get('ps_no', 0.0))
            ps_co = float(record.get('ps_co', 0.0))

            # Initialize customer balance if not exists
            if customer_code not in customer_balances:
                customer_balances[customer_code] = 0.0

            # Update running balance for this customer
            customer_balances[customer_code] += ps_no - ps_co

            # Format balance columns based on positive/negative
            running_balance = customer_balances[customer_code]
            if running_balance > 0:
                record['du_no'] = abs(running_balance)
                record['du_co'] = 0.0
            elif running_balance < 0:
                record['du_no'] = 0.0
                record['du_co'] = abs(running_balance)
            else:
                record['du_no'] = 0.0
                record['du_co'] = 0.0
