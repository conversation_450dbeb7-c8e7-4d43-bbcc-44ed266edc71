"""
S<PERSON> Chi Tiết Công <PERSON>ợ Nhiều Khách Hàng Service

Triển khai ERP Chuyên nghiệp - 20+ năm kinh nghiệm
T<PERSON>h toán số dư công nợ khách hàng theo chuẩn ERP quốc tế

SAME structure as Bang Can Doi Phat Sinh Cong No but:
- tk (account): REQUIRED, single account only
- ma_kh (customer): Optional, empty = all customers, value = specific customer
- Returns balance summary (same as Bang Can Doi)
"""

from datetime import datetime, date
from typing import Dict, Any, List, Optional

from django.utils import timezone
from django_ledger.services.base import BaseService
from django_ledger.utils_new.debt_management import DebtCalculationService
from django_ledger.utils_new.debt_management.debt_calculation import (
    DebtBalanceCalculationUtils,
    DebtBalanceFormatterUtils,
    DebtBalanceQueryUtils
)
from django_ledger.models import CustomerModel, EntityModel


class SoChiTietCongNoNhieuKhachHangService(BaseService):
    """
    Service for So Chi Tiet Cong No Nhieu Khach Hang.
    SAME logic as Bang Can Doi Phat Sinh Cong No but for single account.
    """

    def __init__(self):
        super().__init__()
        self.debt_service = None

    def generate_report(
        self, entity_slug: str, filters: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """
        Generate customer debt balance report using expert ERP logic.
        SAME as Bang Can Doi but for single account only.
        """
        try:
            # Get entity - same as Bang Can Doi
            entity = EntityModel.objects.get(slug=entity_slug)

            # Initialize debt service for this entity - same as Bang Can Doi
            if self.debt_service is None:
                self.debt_service = DebtCalculationService(entity_id=str(entity.uuid))

            # Parse filters - same as Bang Can Doi
            parsed_filters = self._parse_enhanced_filters(filters)

            # Get customers based on filters - same as Bang Can Doi
            customers = self._get_filtered_customers(entity, parsed_filters)

            # Generate debt report for each customer - same as Bang Can Doi
            customer_data_list = []
            stt = 1  # Customer data starts from stt=1 (summary will be stt=0)

            # Convert single account to UUID for calculation - similar to Bang Can Doi logic
            from django_ledger.models import AccountModel
            try:
                account = AccountModel.objects.get(
                    code=parsed_filters['tk'],
                    coa_model__entity=entity
                )
                account_uuid = str(account.uuid)
            except AccountModel.DoesNotExist:
                # Return only summary row with zeros if account not found
                return self._create_summary_only_response()

            # Process each customer for the single account - similar to Bang Can Doi
            for customer in customers:
                try:
                    # Use unified calculation method - same as Bang Can Doi
                    customer_data = self._calculate_customer_debt_for_account(
                        customer=customer,
                        account_uuid=account_uuid,
                        account_code=parsed_filters['tk'],
                        parsed_filters=parsed_filters,
                        entity=entity,
                        stt=stt
                    )

                    if customer_data:
                        customer_data_list.append(customer_data)
                        stt += 1

                except Exception:
                    continue

            # Create final report with summary row first
            return self._create_final_report_with_summary(customer_data_list)

        except Exception as e:
            # Log the error for debugging - same as Bang Can Doi
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error generating customer debt balance report: {str(e)}", exc_info=True)

            # Return empty list instead of raising exception - same as Bang Can Doi
            return []

    def _parse_enhanced_filters(self, filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Parse and validate filters for enhanced debt calculation.
        Same as Bang Can Doi but modified for single account requirement.
        """
        from datetime import timedelta

        parsed = {}

        # Parse date range - same as Bang Can Doi
        start_date_str = filters.get('ngay_ct1', '********')
        end_date_str = filters.get('ngay_ct2', '********')

        try:
            parsed['start_date'] = datetime.strptime(start_date_str, '%Y%m%d').date()
            parsed['end_date'] = datetime.strptime(end_date_str, '%Y%m%d').date()
        except ValueError:
            # Default to current year
            current_year = datetime.now().year
            parsed['start_date'] = date(current_year, 1, 1)
            parsed['end_date'] = date(current_year, 12, 31)

        # Parse account - DIFFERENT from Bang Can Doi: single account required
        tk = filters.get('tk', '')
        if not tk:
            raise ValueError("Account code (tk) is required and must not be empty")
        parsed['tk'] = tk

        # Parse customer - DIFFERENT from Bang Can Doi: single customer code or empty
        ma_kh = filters.get('ma_kh', '')
        if isinstance(ma_kh, str):
            parsed['customer_code'] = ma_kh if ma_kh else ''
        else:
            parsed['customer_code'] = ''

        # Parse customer group UUIDs - ENHANCED for UUID filtering
        parsed['nh_kh1'] = filters.get('nh_kh1')  # UUID or None
        parsed['nh_kh2'] = filters.get('nh_kh2')  # UUID or None
        parsed['nh_kh3'] = filters.get('nh_kh3')  # UUID or None

        # Parse region UUID - ENHANCED for UUID filtering
        parsed['rg_code'] = filters.get('rg_code')  # UUID or None

        return parsed

    def _get_filtered_customers(self, entity: EntityModel, filters: Dict[str, Any]) -> List[CustomerModel]:
        """
        Get customers based on filter criteria.
        Same as Bang Can Doi but modified for single customer filtering.
        """
        queryset = CustomerModel.objects.filter(entity=entity, hidden=False)

        # Filter by single customer code - DIFFERENT from Bang Can Doi
        customer_code = filters.get('customer_code', '').strip()
        if customer_code:
            # If customer code is specified, return only that specific customer
            queryset = queryset.filter(customer_code=customer_code)

        # Filter by customer group UUIDs - ENHANCED for UUID filtering
        if filters.get('nh_kh1'):
            queryset = queryset.filter(customer_group1_id=filters['nh_kh1'])

        if filters.get('nh_kh2'):
            queryset = queryset.filter(customer_group2_id=filters['nh_kh2'])

        if filters.get('nh_kh3'):
            queryset = queryset.filter(customer_group3_id=filters['nh_kh3'])

        # Filter by region UUID - ENHANCED for UUID filtering
        if filters.get('rg_code'):
            queryset = queryset.filter(region_id=filters['rg_code'])

        # If no specific filters, return all customers (backward compatibility) - same as Bang Can Doi
        return list(queryset.order_by('customer_code'))

    def _calculate_customer_debt_for_account(
        self,
        customer: CustomerModel,
        account_uuid: str,
        account_code: str,
        parsed_filters: Dict[str, Any],
        entity: EntityModel,
        stt: int
    ) -> Optional[Dict[str, Any]]:
        """
        Calculate debt data for a specific customer and account.
        SAME logic as Bang Can Doi Phat Sinh Cong No.
        """
        try:
            # TYPE 1: Calculate opening balance using UUID
            from datetime import timedelta
            opening_date = parsed_filters['start_date'] - timedelta(days=1)
            opening_balance = DebtBalanceCalculationUtils.calculate_opening_balance(
                customer=customer,
                end_date=opening_date,
                account_uuids=[account_uuid],  # Use UUID
                entity_id=str(entity.uuid)
            )

            # TYPE 2: Calculate period movements using UUID
            period_debit, period_credit = DebtBalanceCalculationUtils.calculate_period_movements(
                customer=customer,
                start_date=parsed_filters['start_date'],
                end_date=parsed_filters['end_date'],
                account_uuids=[account_uuid],  # Use UUID
                entity_id=str(entity.uuid)
            )

            # TYPE 3: Calculate closing balance
            net_closing_balance = DebtBalanceCalculationUtils.calculate_closing_balance(
                opening_balance=opening_balance,
                period_debit=period_debit,
                period_credit=period_credit
            )

            # Format customer debt data using unified utils
            customer_data = DebtBalanceFormatterUtils.format_customer_debt_record(
                stt=stt,
                customer=customer,
                account_code=account_code,
                opening_balance=opening_balance,
                period_debit=period_debit,
                period_credit=period_credit,
                closing_balance=net_closing_balance
            )

            # Only return data if customer has non-zero balances or transactions
            if (customer_data['no_dk'] != 0 or customer_data['co_dk'] != 0 or
                customer_data['ps_no'] != 0 or customer_data['ps_co'] != 0 or
                customer_data['no_ck'] != 0 or customer_data['co_ck'] != 0):
                return customer_data

            return None

        except Exception:
            return None

    def _create_summary_only_response(self) -> List[Dict[str, Any]]:
        """
        Create response with only summary row (all zeros) when no data found.
        """
        summary_row = {
            'stt': 0,
            'tk': '',
            'ma_kh': '',
            'ten_kh': 'Tổng cộng',
            'no_dk': 0.0,
            'co_dk': 0.0,
            'ps_no': 0.0,
            'ps_co': 0.0,
            'no_ck': 0.0,
            'co_ck': 0.0,
            'bac_ct': 0
        }
        return [summary_row]

    def _create_final_report_with_summary(self, customer_data_list: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Create final report with summary row first, followed by customer data.

        Parameters
        ----------
        customer_data_list : List[Dict[str, Any]]
            List of customer debt records

        Returns
        -------
        List[Dict[str, Any]]
            Final report with summary row first
        """
        # Calculate totals from all customer records
        total_no_dk = sum(record.get('no_dk', 0.0) for record in customer_data_list)
        total_co_dk = sum(record.get('co_dk', 0.0) for record in customer_data_list)
        total_ps_no = sum(record.get('ps_no', 0.0) for record in customer_data_list)
        total_ps_co = sum(record.get('ps_co', 0.0) for record in customer_data_list)
        total_no_ck = sum(record.get('no_ck', 0.0) for record in customer_data_list)
        total_co_ck = sum(record.get('co_ck', 0.0) for record in customer_data_list)

        # Create summary row (stt=0, first row)
        summary_row = {
            'stt': 0,
            'tk': '',
            'ma_kh': '',
            'ten_kh': 'Tổng cộng',
            'no_dk': total_no_dk,
            'co_dk': total_co_dk,
            'ps_no': total_ps_no,
            'ps_co': total_ps_co,
            'no_ck': total_no_ck,
            'co_ck': total_co_ck,
            'bac_ct': 0
        }

        # Return summary row first, then customer data
        return [summary_row] + customer_data_list
