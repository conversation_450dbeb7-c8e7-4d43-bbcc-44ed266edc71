"""
Customer Filtering Utils for ERP Debt Management

Provides reusable UUID-based filtering for customer groups and regions.
Used by both Bang Can Doi and <PERSON> Chi Tiet Cong No APIs.
"""

from typing import Dict, Any, List, Optional
from django.db.models import QuerySet
from django_ledger.models import CustomerModel, EntityModel


class CustomerFilteringUtils:
    """
    Utility class for UUID-based customer filtering.
    
    Supports filtering by:
    - nh_kh1: Customer Group 1 UUID
    - nh_kh2: Customer Group 2 UUID  
    - nh_kh3: Customer Group 3 UUID
    - rg_code: Region UUID
    """

    @staticmethod
    def parse_uuid_filters(filters: Dict[str, Any]) -> Dict[str, Optional[str]]:
        """
        Parse UUID filters from request data.
        
        Parameters
        ----------
        filters : Dict[str, Any]
            Raw filter data from request
            
        Returns
        -------
        Dict[str, Optional[str]]
            Parsed UUID filters
        """
        return {
            'nh_kh1': filters.get('nh_kh1'),  # Customer Group 1 UUID
            'nh_kh2': filters.get('nh_kh2'),  # Customer Group 2 UUID
            'nh_kh3': filters.get('nh_kh3'),  # Customer Group 3 UUID
            'rg_code': filters.get('rg_code')  # Region UUID
        }

    @staticmethod
    def apply_uuid_filters(
        queryset: QuerySet, 
        uuid_filters: Dict[str, Optional[str]]
    ) -> QuerySet:
        """
        Apply UUID-based filters to CustomerModel queryset.
        
        Parameters
        ----------
        queryset : QuerySet
            Base CustomerModel queryset
        uuid_filters : Dict[str, Optional[str]]
            UUID filters to apply
            
        Returns
        -------
        QuerySet
            Filtered queryset
        """
        # Filter by Customer Group 1 UUID
        if uuid_filters.get('nh_kh1'):
            queryset = queryset.filter(customer_group1_id=uuid_filters['nh_kh1'])
        
        # Filter by Customer Group 2 UUID
        if uuid_filters.get('nh_kh2'):
            queryset = queryset.filter(customer_group2_id=uuid_filters['nh_kh2'])
            
        # Filter by Customer Group 3 UUID
        if uuid_filters.get('nh_kh3'):
            queryset = queryset.filter(customer_group3_id=uuid_filters['nh_kh3'])
        
        # Filter by Region UUID
        if uuid_filters.get('rg_code'):
            queryset = queryset.filter(region_id=uuid_filters['rg_code'])
        
        return queryset

    @staticmethod
    def get_filtered_customers(
        entity: EntityModel, 
        filters: Dict[str, Any]
    ) -> List[CustomerModel]:
        """
        Get customers filtered by UUID criteria.
        
        Parameters
        ----------
        entity : EntityModel
            Entity to filter customers for
        filters : Dict[str, Any]
            Filter criteria including UUID filters
            
        Returns
        -------
        List[CustomerModel]
            Filtered customers list
        """
        # Base queryset
        queryset = CustomerModel.objects.filter(entity=entity, hidden=False)
        
        # Apply single customer filter if specified
        customer_code = filters.get('customer_code', '').strip()
        if customer_code:
            queryset = queryset.filter(customer_code=customer_code)
        
        # Parse and apply UUID filters
        uuid_filters = CustomerFilteringUtils.parse_uuid_filters(filters)
        queryset = CustomerFilteringUtils.apply_uuid_filters(queryset, uuid_filters)
        
        # Return ordered list
        return list(queryset.order_by('customer_code'))

    @staticmethod
    def validate_uuid_filters(filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate UUID filters format.
        
        Parameters
        ----------
        filters : Dict[str, Any]
            Raw filters to validate
            
        Returns
        -------
        Dict[str, Any]
            Validated filters
            
        Raises
        ------
        ValueError
            If UUID format is invalid
        """
        import uuid
        
        uuid_fields = ['nh_kh1', 'nh_kh2', 'nh_kh3', 'rg_code']
        
        for field in uuid_fields:
            value = filters.get(field)
            if value is not None:
                try:
                    # Validate UUID format
                    uuid.UUID(str(value))
                except (ValueError, TypeError):
                    raise ValueError(f"Invalid UUID format for {field}: {value}")
        
        return filters

    @staticmethod
    def get_filter_summary(filters: Dict[str, Any]) -> Dict[str, Any]:
        """
        Get summary of applied filters for logging/debugging.
        
        Parameters
        ----------
        filters : Dict[str, Any]
            Applied filters
            
        Returns
        -------
        Dict[str, Any]
            Filter summary
        """
        uuid_filters = CustomerFilteringUtils.parse_uuid_filters(filters)
        
        summary = {
            'customer_code': filters.get('customer_code', ''),
            'uuid_filters_applied': []
        }
        
        for field, value in uuid_filters.items():
            if value:
                summary['uuid_filters_applied'].append({
                    'field': field,
                    'uuid': str(value)[:8] + '...'  # Truncated for readability
                })
        
        return summary
