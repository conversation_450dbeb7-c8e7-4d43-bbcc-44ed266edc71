"""
Django Ledger - Dịch vụ Tính toán Công nợ

Module này cung cấp chức năng tính toán công nợ toàn diện cho hệ thống ERP,
hỗ trợ cả công nợ phải thu khách hàng và phải trả nhà cung cấp với đa sổ cái.

Tạo bởi: Chuyên gia ERP & Lậ<PERSON> tr<PERSON>nh (20 năm kinh nghiệm)
Ngày: 2024-12-19
"""

from decimal import Decimal
from typing import Dict, List, Optional, Union
from datetime import date, datetime

from django.db.models import Q, Sum, F, Count
from django.db.models.query import QuerySet
from django.utils import timezone

from django_ledger.models import (
    TransactionModel,
    JournalEntryModel,
    LedgerModel,
    CustomerModel,
    VendorModel,
    AccountModel
)


class DebtCalculationService:
    """
    Lớp dịch vụ tính toán công nợ khách hàng và nhà cung cấp với bộ lọc nâng cao
    và hỗ trợ đa sổ cái dựa trên thực tiễn tốt nhất của ERP.
    """

    # Tiền tố mã tài khoản cho tính toán công nợ
    CUSTOMER_RECEIVABLE_ACCOUNTS = ['131', '132', '133', '134', '135', '136', '137', '138']  # Phải thu khách hàng
    SUPPLIER_PAYABLE_ACCOUNTS = ['331', '332', '333', '334', '335', '336', '337', '338']    # Phải trả nhà cung cấp

    def __init__(self, entity_id: str = None):
        """
        Khởi tạo dịch vụ tính toán công nợ.

        Args:
            entity_id: ID đơn vị tùy chọn để giới hạn phạm vi tính toán
        """
        self.entity_id = entity_id

    def get_customer_debt_summary(self, customer_id: str, as_of_date: date = None) -> Dict:
        """
        Tính tổng hợp công nợ khách hàng trên tất cả sổ cái.

        Args:
            customer_id: UUID của khách hàng
            as_of_date: Tính công nợ tại ngày này (mặc định: hôm nay)

        Returns:
            Dict chứa tổng hợp công nợ với phân tích theo sổ cái
        """
        if as_of_date is None:
            as_of_date = timezone.now().date()

        # Xây dựng truy vấn cơ sở cho phải thu khách hàng
        base_query = self._build_customer_debt_query(customer_id, as_of_date)

        # Tính tổng
        result = base_query.aggregate(
            total_debit=Sum('amount', filter=Q(tx_type='DEBIT')),
            total_credit=Sum('amount', filter=Q(tx_type='CREDIT'))
        )

        total_debit = result['total_debit'] or Decimal('0.00')
        total_credit = result['total_credit'] or Decimal('0.00')
        net_debt = total_debit - total_credit

        # Lấy phân tích theo sổ cái
        ledger_breakdown = self.get_customer_debt_by_ledger(customer_id, as_of_date)

        return {
            'customer_id': customer_id,
            'as_of_date': as_of_date,
            'total_debit': total_debit,
            'total_credit': total_credit,
            'net_debt': net_debt,
            'currency': 'VND',  # Đơn vị tiền tệ mặc định
            'breakdown_by_ledger': ledger_breakdown,
            'calculation_timestamp': timezone.now()
        }

    def get_customer_debt_by_ledger(self, customer_id: str, as_of_date: date = None) -> List[Dict]:
        """
        Tính toán chi tiết công nợ khách hàng theo từng sổ cái.

        Args:
            customer_id: UUID của khách hàng
            as_of_date: Tính công nợ tại ngày này

        Returns:
            Danh sách các dict chứa chi tiết công nợ theo sổ cái
        """
        if as_of_date is None:
            as_of_date = timezone.now().date()

        base_query = self._build_customer_debt_query(customer_id, as_of_date)

        # Nhóm theo sổ cái
        ledger_debts = base_query.values(
            'journal_entry__ledger__name',
            'journal_entry__ledger__uuid',
            'journal_entry__ledger__posted',
            'journal_entry__ledger__locked'
        ).annotate(
            total_debit=Sum('amount', filter=Q(tx_type='DEBIT')),
            total_credit=Sum('amount', filter=Q(tx_type='CREDIT')),
            transaction_count=Count('uuid')
        ).order_by('-total_debit')

        # Tính công nợ ròng cho mỗi sổ cái
        result = []
        for ledger_data in ledger_debts:
            debit = ledger_data['total_debit'] or Decimal('0.00')
            credit = ledger_data['total_credit'] or Decimal('0.00')
            net_debt = debit - credit

            result.append({
                'ledger_name': ledger_data['journal_entry__ledger__name'],
                'ledger_uuid': ledger_data['journal_entry__ledger__uuid'],
                'ledger_posted': ledger_data['journal_entry__ledger__posted'],
                'ledger_locked': ledger_data['journal_entry__ledger__locked'],
                'total_debit': debit,
                'total_credit': credit,
                'net_debt': net_debt,
                'transaction_count': ledger_data['transaction_count']
            })

        return result

    def get_supplier_debt_summary(self, supplier_id: str, as_of_date: date = None) -> Dict:
        """
        Tính tổng hợp công nợ nhà cung cấp trên tất cả sổ cái.

        Args:
            supplier_id: UUID của nhà cung cấp
            as_of_date: Tính công nợ tại ngày này (mặc định: hôm nay)

        Returns:
            Dict chứa tổng hợp công nợ với phân tích theo sổ cái
        """
        if as_of_date is None:
            as_of_date = timezone.now().date()

        # Xây dựng truy vấn cơ sở cho phải trả nhà cung cấp
        base_query = self._build_supplier_debt_query(supplier_id, as_of_date)

        # Tính tổng
        result = base_query.aggregate(
            total_debit=Sum('amount', filter=Q(tx_type='DEBIT')),
            total_credit=Sum('amount', filter=Q(tx_type='CREDIT'))
        )

        total_debit = result['total_debit'] or Decimal('0.00')
        total_credit = result['total_credit'] or Decimal('0.00')
        net_debt = total_credit - total_debit  # Đối với phải trả, số Có làm tăng công nợ

        # Lấy phân tích theo sổ cái
        ledger_breakdown = self.get_supplier_debt_by_ledger(supplier_id, as_of_date)

        return {
            'supplier_id': supplier_id,
            'as_of_date': as_of_date,
            'total_debit': total_debit,
            'total_credit': total_credit,
            'net_debt': net_debt,
            'currency': 'VND',
            'breakdown_by_ledger': ledger_breakdown,
            'calculation_timestamp': timezone.now()
        }

    def get_supplier_debt_by_ledger(self, supplier_id: str, as_of_date: date = None) -> List[Dict]:
        """
        Tính toán chi tiết công nợ nhà cung cấp theo từng sổ cái.

        Args:
            supplier_id: UUID của nhà cung cấp
            as_of_date: Tính công nợ tại ngày này

        Returns:
            Danh sách các dict chứa chi tiết công nợ theo sổ cái
        """
        if as_of_date is None:
            as_of_date = timezone.now().date()

        base_query = self._build_supplier_debt_query(supplier_id, as_of_date)

        # Nhóm theo sổ cái
        ledger_debts = base_query.values(
            'journal_entry__ledger__name',
            'journal_entry__ledger__uuid',
            'journal_entry__ledger__posted',
            'journal_entry__ledger__locked'
        ).annotate(
            total_debit=Sum('amount', filter=Q(tx_type='DEBIT')),
            total_credit=Sum('amount', filter=Q(tx_type='CREDIT')),
            transaction_count=Count('uuid')
        ).order_by('-total_credit')

        # Tính công nợ ròng cho mỗi sổ cái
        result = []
        for ledger_data in ledger_debts:
            debit = ledger_data['total_debit'] or Decimal('0.00')
            credit = ledger_data['total_credit'] or Decimal('0.00')
            net_debt = credit - debit  # Đối với phải trả, số Có làm tăng công nợ

            result.append({
                'ledger_name': ledger_data['journal_entry__ledger__name'],
                'ledger_uuid': ledger_data['journal_entry__ledger__uuid'],
                'ledger_posted': ledger_data['journal_entry__ledger__posted'],
                'ledger_locked': ledger_data['journal_entry__ledger__locked'],
                'total_debit': debit,
                'total_credit': credit,
                'net_debt': net_debt,
                'transaction_count': ledger_data['transaction_count']
            })

        return result

    def _build_customer_debt_query(self, customer_id: str, as_of_date: date) -> QuerySet:
        """
        Xây dựng truy vấn cơ sở để tính toán công nợ khách hàng.

        Args:
            customer_id: UUID của khách hàng
            as_of_date: Tính công nợ tại ngày này

        Returns:
            QuerySet cho các giao dịch công nợ khách hàng
        """
        # Xây dựng bộ lọc tài khoản cho các khoản phải thu
        account_filter = Q()
        for prefix in self.CUSTOMER_RECEIVABLE_ACCOUNTS:
            account_filter |= Q(account__code__startswith=prefix)

        query = TransactionModel.objects.filter(
            journal_entry__customer_id=customer_id,
            journal_entry__timestamp__date__lte=as_of_date,
            journal_entry__ledger__posted=True,  # Chỉ các sổ cái đã ghi sổ
            journal_entry__ledger__hidden=False,  # Loại trừ các sổ cái ẩn
        ).filter(account_filter)

        # Thêm bộ lọc đơn vị nếu được chỉ định
        if self.entity_id:
            query = query.filter(journal_entry__ledger__entity_id=self.entity_id)

        return query.select_related(
            'journal_entry',
            'journal_entry__ledger',
            'journal_entry__customer',
            'account'
        )

    def _build_vendor_debt_query(self, vendor_id: str, as_of_date: date) -> QuerySet:
        """
        Xây dựng truy vấn cơ sở để tính toán công nợ nhà cung cấp.
        Lưu ý: Nhà cung cấp được lưu trong CustomerModel với is_vendor=True.

        Args:
            vendor_id: UUID của nhà cung cấp (CustomerModel với is_vendor=True)
            as_of_date: Tính công nợ tại ngày này

        Returns:
            QuerySet cho các giao dịch công nợ nhà cung cấp
        """
        # Xây dựng bộ lọc tài khoản cho các khoản phải trả
        account_filter = Q()
        for prefix in self.SUPPLIER_PAYABLE_ACCOUNTS:
            account_filter |= Q(account__code__startswith=prefix)

        query = TransactionModel.objects.filter(
            journal_entry__customer_id=vendor_id, # Sử dụng customer_id vì nhà cung cấp cũng là CustomerModel
            journal_entry__customer__is_vendor=True,  # Đảm bảo đây là nhà cung cấp
            journal_entry__timestamp__date__lte=as_of_date,
            journal_entry__ledger__posted=True,  # Chỉ các sổ cái đã ghi sổ
            journal_entry__ledger__hidden=False,  # Loại trừ các sổ cái ẩn
        ).filter(account_filter)

        # Thêm bộ lọc đơn vị nếu được chỉ định
        if self.entity_id:
            query = query.filter(journal_entry__ledger__entity_id=self.entity_id)

        return query.select_related(
            'journal_entry',
            'journal_entry__ledger',
            'journal_entry__customer',
            'account'
        )


# ============================================================================
# DEBT BALANCE CALCULATION UTILITIES
# ============================================================================

class DebtBalanceCalculationUtils:
    """
    🧮 Tiện ích Tính toán Số dư Công nợ Chuyên nghiệp

    Các hàm tiện ích chuyên biệt cho tính toán số dư công nợ khách hàng
    tuân theo chuẩn ERP và nguyên tắc kế toán.

    Sử dụng đặc biệt cho báo cáo "Bảng cân đối phát sinh công nợ".

    Triển khai ERP Chuyên nghiệp - 20+ năm kinh nghiệm
    """

    @staticmethod
    def calculate_amounts_from_transactions(transactions) -> tuple[Decimal, Decimal]:
        """
        Phương thức tính toán số tiền giao dịch thống nhất.

        Phương thức này đảm bảo tính toán nhất quán trên tất cả dịch vụ
        bằng cách xử lý từng giao dịch riêng lẻ sử dụng cùng logic.

        Parameters
        ----------
        transactions : QuerySet
            QuerySet giao dịch để tính toán số tiền

        Returns
        -------
        tuple[Decimal, Decimal]
            (tổng_nợ, tổng_có) số tiền
        """
        from django_ledger.utils_new.debt_management.debt_detail_utils import DebtDetailUtils

        total_debit = Decimal('0.00')
        total_credit = Decimal('0.00')

        for transaction in transactions:
            try:
                _, ps_no, ps_co = DebtDetailUtils.calculate_corresponding_account_and_amounts(
                    transaction, transaction.journal_entry
                )
                total_debit += Decimal(str(ps_no))
                total_credit += Decimal(str(ps_co))
            except Exception:
                # Bỏ qua các giao dịch có vấn đề
                continue

        return total_debit, total_credit

    @staticmethod
    def get_customer_debt_accounts(
        customer: 'CustomerModel',
        entity_id: str = None,
        user_selected_account: str = None,
        user_selected_account_uuid: str = None
    ) -> List[str]:
        """
        Lấy tất cả tài khoản công nợ của khách hàng dựa trên giao dịch NỢ.

        Phương Pháp 1: Theo Tài Khoản Nợ Trong Bút Toán
        - Lấy tất cả tài khoản mà khách hàng này có giao dịch NỢ
        - Phát hiện động, không hardcode mã tài khoản

        Parameters
        ----------
        customer : CustomerModel
            Khách hàng để lấy tài khoản công nợ
        entity_id : str, optional
            ID đơn vị để giới hạn phạm vi tính toán
        user_selected_account : str, optional
            Mã tài khoản cụ thể do người dùng chọn
        user_selected_account_uuid : str, optional
            UUID tài khoản cụ thể do người dùng chọn (ưu tiên hơn)

        Returns
        -------
        List[str]
            Danh sách mã tài khoản có giao dịch nợ cho khách hàng này
        """
        from django_ledger.models import TransactionModel, AccountModel

        # Nếu người dùng chọn UUID tài khoản cụ thể, chuyển đổi sang mã và trả về
        if user_selected_account_uuid:
            try:
                account = AccountModel.objects.get(uuid=user_selected_account_uuid)
                return [account.code]
            except AccountModel.DoesNotExist:
                return []

        # Nếu người dùng chọn mã tài khoản cụ thể, trả về mã đó
        if user_selected_account:
            return [user_selected_account]

        # Xây dựng truy vấn cơ sở - kiểm tra cả DEBIT và debit (không phân biệt chữ hoa chữ thường)
        from django.db.models import Q
        query = TransactionModel.objects.filter(
            journal_entry__customer=customer,
            journal_entry__ledger__posted=True,
            journal_entry__ledger__hidden=False
        ).filter(
            Q(tx_type='DEBIT') | Q(tx_type='debit')  # Hỗ trợ cả hai trường hợp
        )

        # Thêm bộ lọc đơn vị nếu được chỉ định
        if entity_id:
            query = query.filter(journal_entry__ledger__entity_id=entity_id)

        # Lấy các mã tài khoản riêng biệt
        debt_accounts = query.values_list('account__code', flat=True).distinct()

        # Chuyển đổi sang danh sách và loại bỏ các mục trùng lặp
        account_list = list(set(debt_accounts))
        account_list.sort()  # Sắp xếp để có thứ tự nhất quán

        return account_list

    @staticmethod
    def calculate_opening_balance(
        customer: 'CustomerModel',
        end_date: date,
        account_codes: List[str] = None,
        account_uuids: List[str] = None,
        entity_id: str = None
    ) -> Decimal:
        """
        Tính số dư đầu kỳ của khách hàng đến một ngày cụ thể.

        Cách tính LOẠI 1: Số dư đầu kỳ (Dư đầu kỳ)
        - Tổng tất cả các giao dịch từ đầu đến end_date (bao gồm cả end_date)
        - Nợ làm tăng các khoản phải thu (dương)
        - Có làm giảm các khoản phải thu (âm)

        Parameters
        ----------
        customer : CustomerModel
            Khách hàng để tính số dư đầu kỳ
        end_date : date
            Tính số dư đến ngày này (bao gồm cả ngày này)
        account_codes : List[str], optional
            Các mã tài khoản để lọc (mặc định: tự động phát hiện)
        account_uuids : List[str], optional
            Các UUID tài khoản để lọc (ưu tiên hơn mã)
        entity_id : str, optional
            ID đơn vị để giới hạn phạm vi tính toán

        Returns
        -------
        Decimal
            Số dư đầu kỳ ròng (dương = khách hàng nợ chúng ta, âm = chúng ta nợ khách hàng)
        """
        # Xây dựng bộ lọc tài khoản - ưu tiên UUID hơn mã
        account_filter = Q()

        if account_uuids:
            # Sử dụng bộ lọc UUID (ưu tiên hơn)
            for uuid_val in account_uuids:
                account_filter |= Q(account__uuid=uuid_val)
        elif account_codes:
            # Sử dụng bộ lọc mã
            for code in account_codes:
                account_filter |= Q(account__code=code)  # Khớp chính xác, không phải startswith
        else:
            # Tự động phát hiện tài khoản công nợ nếu không có tài khoản nào được cung cấp
            account_codes = DebtBalanceCalculationUtils.get_customer_debt_accounts(
                customer=customer,
                entity_id=entity_id
            )
            if not account_codes:
                return Decimal('0.00')

            for code in account_codes:
                account_filter |= Q(account__code=code)

        # Xây dựng truy vấn cơ sở - lấy TẤT CẢ các giao dịch (nợ và có)
        query = TransactionModel.objects.filter(
            journal_entry__customer=customer,
            journal_entry__timestamp__date__lte=end_date,
            journal_entry__ledger__posted=True,
            journal_entry__ledger__hidden=False,
            amount__gt=0  # Chỉ các giao dịch có số tiền > 0
        ).filter(account_filter)

        # Thêm bộ lọc đơn vị nếu được chỉ định
        if entity_id:
            query = query.filter(journal_entry__ledger__entity_id=entity_id)

        # Sử dụng phương pháp tính toán thống nhất để đảm bảo tính nhất quán
        total_debit, total_credit = DebtBalanceCalculationUtils.calculate_amounts_from_transactions(query)
        opening_balance = total_debit - total_credit

        return opening_balance

    @staticmethod
    def calculate_period_movements(
        customer: 'CustomerModel',
        start_date: date,
        end_date: date,
        account_codes: List[str] = None,
        account_uuids: List[str] = None,
        entity_id: str = None
    ) -> tuple[Decimal, Decimal]:
        """
        Tính toán biến động trong kỳ của khách hàng (riêng biệt nợ và có).

        Cách tính LOẠI 2: Biến động trong kỳ (Phát sinh trong kỳ)
        - Tính toán riêng biệt các biến động nợ và có trong kỳ
        - Trả về cả hai giá trị dưới dạng số dương
        - Các giá trị này đại diện cho các biến động giao dịch thực tế, không phải số dư ròng

        Parameters
        ----------
        customer : CustomerModel
            Khách hàng để tính toán biến động
        start_date : date
            Ngày bắt đầu kỳ (bao gồm cả ngày này)
        end_date : date
            Ngày kết thúc kỳ (bao gồm cả ngày này)
        account_codes : List[str], optional
            Các mã tài khoản để lọc (mặc định: tự động phát hiện)
        account_uuids : List[str], optional
            Các UUID tài khoản để lọc (ưu tiên hơn mã)
        entity_id : str, optional
            ID đơn vị để giới hạn phạm vi tính toán

        Returns
        -------
        tuple[Decimal, Decimal]
            (phat_sinh_no_trong_ky, phat_sinh_co_trong_ky) - cả hai đều là số dương
        """
        # Xây dựng bộ lọc tài khoản - ưu tiên UUID hơn mã
        account_filter = Q()

        if account_uuids:
            # Sử dụng bộ lọc UUID (ưu tiên hơn)
            for uuid_val in account_uuids:
                account_filter |= Q(account__uuid=uuid_val)
        elif account_codes:
            # Sử dụng bộ lọc mã
            for code in account_codes:
                account_filter |= Q(account__code=code)  # Khớp chính xác, không phải startswith
        else:
            # Tự động phát hiện tài khoản công nợ nếu không có tài khoản nào được cung cấp
            account_codes = DebtBalanceCalculationUtils.get_customer_debt_accounts(
                customer=customer,
                entity_id=entity_id
            )
            if not account_codes:
                return Decimal('0.00'), Decimal('0.00')

            for code in account_codes:
                account_filter |= Q(account__code=code)

        # Xây dựng truy vấn cơ sở cho kỳ
        query = TransactionModel.objects.filter(
            journal_entry__customer=customer,
            journal_entry__timestamp__date__gte=start_date,
            journal_entry__timestamp__date__lte=end_date,
            journal_entry__ledger__posted=True,
            journal_entry__ledger__hidden=False
        ).filter(account_filter)

        # Thêm bộ lọc đơn vị nếu được chỉ định
        if entity_id:
            query = query.filter(journal_entry__ledger__entity_id=entity_id)

        # Sử dụng phương pháp tính toán thống nhất để đảm bảo tính nhất quán
        period_debit, period_credit = DebtBalanceCalculationUtils.calculate_amounts_from_transactions(query)

        return period_debit, period_credit

    @staticmethod
    def calculate_closing_balance(
        opening_balance: Decimal,
        period_debit: Decimal,
        period_credit: Decimal
    ) -> Decimal:
        """
        Tính số dư cuối kỳ bằng công thức ERP.

        Cách tính LOẠI 3: Số dư cuối kỳ (Dư cuối kỳ)
        Công thức: Cuối kỳ = Đầu kỳ + Phát sinh Nợ - Phát sinh Có

        Parameters
        ----------
        opening_balance : Decimal
            Số dư đầu kỳ
        period_debit : Decimal
            Biến động Nợ trong kỳ
        period_credit : Decimal
            Biến động Có trong kỳ

        Returns
        -------
        Decimal
            Số dư cuối kỳ (dương = khách hàng nợ chúng ta, âm = chúng ta nợ khách hàng)
        """
        return opening_balance + period_debit - period_credit

    @staticmethod
    def validate_accounting_equation(
        opening_balance: Decimal,
        period_debit: Decimal,
        period_credit: Decimal,
        closing_balance: Decimal,
        tolerance: Decimal = Decimal('0.01')
    ) -> bool:
        """
        Xác thực phương trình kế toán cho tính toán công nợ.

        Phương trình: Cuối kỳ = Đầu kỳ + Phát sinh Nợ - Phát sinh Có

        Parameters
        ----------
        opening_balance : Decimal
            Số dư đầu kỳ
        period_debit : Decimal
            Biến động Nợ trong kỳ
        period_credit : Decimal
            Biến động Có trong kỳ
        closing_balance : Decimal
            Số dư cuối kỳ
        tolerance : Decimal, optional
            Sai số cho phép cho sự khác biệt làm tròn (mặc định: 0.01)

        Returns
        -------
        bool
            True nếu phương trình hợp lệ trong phạm vi sai số cho phép
        """
        expected_closing = opening_balance + period_debit - period_credit
        return abs(closing_balance - expected_closing) <= tolerance

    @staticmethod
    def get_customer_transactions_detail(
        customer: 'CustomerModel',
        start_date: date,
        end_date: date,
        account_codes: List[str] = None,
        entity_id: str = None
    ) -> List[Dict]:
        """
        Lấy danh sách giao dịch chi tiết cho một khách hàng trong một khoảng thời gian.

        Hữu ích cho việc gỡ lỗi và phân tích chi tiết.

        Parameters
        ----------
        customer : CustomerModel
            Khách hàng để lấy giao dịch
        start_date : date
            Ngày bắt đầu kỳ (bao gồm cả ngày này)
        end_date : date
            Ngày kết thúc kỳ (bao gồm cả ngày này)
        account_codes : List[str], optional
            Các mã tài khoản để lọc (mặc định: ['131'])
        entity_id : str, optional
            ID đơn vị để giới hạn phạm vi tính toán

        Returns
        -------
        List[Dict]
            Danh sách chi tiết giao dịch
        """
        if account_codes is None:
            account_codes = ['131'] # Mặc định tài khoản phải thu khách hàng

        # Xây dựng bộ lọc tài khoản
        account_filter = Q()
        for code in account_codes:
            account_filter |= Q(account__code__startswith=code)

        # Xây dựng truy vấn cơ sở - lấy TẤT CẢ các giao dịch (nợ và có)
        query = TransactionModel.objects.filter(
            journal_entry__customer=customer,
            journal_entry__timestamp__date__gte=start_date,
            journal_entry__timestamp__date__lte=end_date,
            journal_entry__ledger__posted=True,
            journal_entry__ledger__hidden=False,
            amount__gt=0  # Chỉ các giao dịch có số tiền > 0
        ).filter(account_filter)

        # Thêm bộ lọc đơn vị nếu được chỉ định
        if entity_id:
            query = query.filter(journal_entry__ledger__entity_id=entity_id)

        # Lấy chi tiết giao dịch
        transactions = query.select_related(
            'journal_entry',
            'journal_entry__ledger',
            'account'
        ).order_by('journal_entry__timestamp')

        result = []
        for txn in transactions:
            result.append({
                'transaction_id': str(txn.uuid),
                'journal_entry_id': str(txn.journal_entry.uuid),
                'ledger_name': txn.journal_entry.ledger.name,
                'account_code': txn.account.code,
                'account_name': txn.account.name,
                'tx_type': txn.tx_type,
                'amount': txn.amount,
                'description': txn.description,
                'timestamp': txn.journal_entry.timestamp,
                'posted': txn.journal_entry.ledger.posted
            })

        return result


class DebtBalanceFormatterUtils:
    """
    📊 Tiện ích Định dạng Dữ liệu Số dư Công nợ

    Lớp tiện ích để định dạng dữ liệu số dư công nợ theo tiêu chuẩn ERP.
    Sử dụng cho việc định dạng báo cáo "Bảng cân đối phát sinh công nợ".
    """

    @staticmethod
    def format_balance_columns(net_balance: Decimal) -> tuple[float, float]:
        """
        Định dạng số dư ròng thành các cột nợ/có.

        Tiêu chuẩn ERP:
        - Số tiền dương → Cột Nợ (khách hàng nợ chúng ta)
        - Số tiền âm → Cột Có (chúng ta nợ khách hàng)
        - Luôn hiển thị số dương trong các cột tương ứng

        Parameters
        ----------
        net_balance : Decimal
            Số dư ròng

        Returns
        -------
        tuple[float, float]
            (so_tien_no, so_tien_co) - một trong hai sẽ là 0.0
        """
        if net_balance > 0:
            return float(net_balance), 0.0
        elif net_balance < 0:
            return 0.0, float(-net_balance)
        else:
            return 0.0, 0.0

    @staticmethod
    def format_customer_debt_record(
        stt: int,
        customer: 'CustomerModel',
        account_code: str,
        opening_balance: Decimal,
        period_debit: Decimal,
        period_credit: Decimal,
        closing_balance: Decimal,
        additional_fields: Dict = None
    ) -> Dict[str, any]:
        """
        Định dạng bản ghi công nợ khách hàng hoàn chỉnh cho phản hồi API.

        Parameters
        ----------
        stt : int
            Số thứ tự
        customer : CustomerModel
            Đối tượng CustomerModel
        account_code : str
            Mã tài khoản được sử dụng
        opening_balance : Decimal
            Số dư đầu kỳ
        period_debit : Decimal
            Biến động Nợ trong kỳ
        period_credit : Decimal
            Biến động Có trong kỳ
        closing_balance : Decimal
            Số dư cuối kỳ
        additional_fields : Dict, optional
            Các trường bổ sung để bao gồm trong bản ghi

        Returns
        -------
        Dict[str, Any]
            Bản ghi công nợ khách hàng đã được định dạng
        """
        # Định dạng số dư đầu kỳ
        no_dk, co_dk = DebtBalanceFormatterUtils.format_balance_columns(opening_balance)

        # Định dạng số dư cuối kỳ
        no_ck, co_ck = DebtBalanceFormatterUtils.format_balance_columns(closing_balance)

        # Cấu trúc bản ghi cơ sở
        record = {
            'stt': stt,
            'tk': account_code,
            'ma_kh': customer.customer_number or customer.customer_code or str(customer.uuid)[:8],
            'ten_kh': customer.customer_name,

            # TYPE 1: Opening Balance
            'no_dk': no_dk,
            'co_dk': co_dk,

            # TYPE 2: Period Movements
            'ps_no': float(period_debit),
            'ps_co': float(period_credit),

            # TYPE 3: Closing Balance
            'no_ck': no_ck,
            'co_ck': co_ck,

            # Document level - required field
            'bac_ct': 1,

            # Customer grouping
            'nhom': getattr(customer, 'nh_kh1', '') or '',
            'nhom1': getattr(customer, 'nh_kh2', '') or '',
            'nhom2': getattr(customer, 'nh_kh3', '') or '',
            'nhom3': '',

            # Enhanced fields for validation
            'total_debit': float(period_debit),
            'total_credit': float(period_credit),
            'opening_balance_raw': float(opening_balance),
            'closing_balance_raw': float(closing_balance),
            'calculation_timestamp': timezone.now(),
            'customer_uuid': str(customer.uuid)
        }

        # Thêm các trường bổ sung nếu được cung cấp
        if additional_fields:
            record.update(additional_fields)

        return record

    @staticmethod
    def calculate_summary_totals(records: List[Dict]) -> Dict[str, float]:
        """
        Tính tổng cộng từ danh sách các bản ghi công nợ.

        Parameters
        ----------
        records : List[Dict]
            Danh sách các bản ghi công nợ đã được định dạng

        Returns
        -------
        Dict[str, float]
            Tổng cộng
        """
        totals = {
            'total_no_dk': 0.0,
            'total_co_dk': 0.0,
            'total_ps_no': 0.0,
            'total_ps_co': 0.0,
            'total_no_ck': 0.0,
            'total_co_ck': 0.0,
            'net_opening': 0.0,
            'net_period': 0.0,
            'net_closing': 0.0,
            'record_count': len(records)
        }

        for record in records:
            totals['total_no_dk'] += record.get('no_dk', 0.0)
            totals['total_co_dk'] += record.get('co_dk', 0.0)
            totals['total_ps_no'] += record.get('ps_no', 0.0)
            totals['total_ps_co'] += record.get('ps_co', 0.0)
            totals['total_no_ck'] += record.get('no_ck', 0.0)
            totals['total_co_ck'] += record.get('co_ck', 0.0)

        # Tính toán các vị thế ròng
        totals['net_opening'] = totals['total_no_dk'] - totals['total_co_dk']
        totals['net_period'] = totals['total_ps_no'] - totals['total_ps_co']
        totals['net_closing'] = totals['total_no_ck'] - totals['total_co_ck']

        return totals


class DebtBalanceQueryUtils:
    """
    🔍 Tiện ích Truy vấn Số dư Công nợ

    Lớp tiện ích để xây dựng các truy vấn cơ sở dữ liệu được tối ưu hóa cho tính toán công nợ.
    """

    @staticmethod
    def build_account_filter(account_codes: List[str]) -> Q:
        """
        Xây dựng bộ lọc Django Q cho các mã tài khoản.

        Parameters
        ----------
        account_codes : List[str]
            Danh sách các mã tài khoản để lọc

        Returns
        -------
        Q
            Đối tượng bộ lọc Django Q
        """
        account_filter = Q()
        for code in account_codes:
            account_filter |= Q(account__code__startswith=code)

        return account_filter

    @staticmethod
    def get_base_transaction_queryset(
        customer: 'CustomerModel',
        entity_id: str = None,
        posted_only: bool = True
    ) -> QuerySet:
        """
        Lấy queryset giao dịch cơ sở cho một khách hàng.

        Parameters
        ----------
        customer : CustomerModel
            Khách hàng để lọc giao dịch
        entity_id : str, optional
            ID đơn vị để giới hạn phạm vi tính toán
        posted_only : bool, optional
            Chỉ bao gồm các giao dịch đã ghi sổ (mặc định: True)

        Returns
        -------
        QuerySet
            Queryset giao dịch cơ sở
        """
        queryset = TransactionModel.objects.filter(
            journal_entry__customer=customer
        )

        if posted_only:
            queryset = queryset.filter(
                journal_entry__ledger__posted=True,
                journal_entry__ledger__hidden=False
            )

        if entity_id:
            queryset = queryset.filter(journal_entry__ledger__entity_id=entity_id)

        return queryset.select_related(
            'journal_entry',
            'journal_entry__ledger',
            'journal_entry__customer',
            'account'
        )

    @staticmethod
    def get_customers_with_transactions(
        entity_id: str,
        start_date: date,
        end_date: date,
        account_codes: List[str] = None
    ) -> QuerySet:
        """
        Lấy danh sách khách hàng có giao dịch trong khoảng thời gian được chỉ định.

        Parameters
        ----------
        entity_id : str
            ID đơn vị để giới hạn phạm vi tính toán
        start_date : date
            Ngày bắt đầu kỳ
        end_date : date
            Ngày kết thúc kỳ
        account_codes : List[str], optional
            Các mã tài khoản để lọc (mặc định: ['131'])

        Returns
        -------
        QuerySet
            Danh sách khách hàng có giao dịch trong kỳ
        """
        if account_codes is None:
            account_codes = ['131'] # Mặc định tài khoản phải thu khách hàng

        # Xây dựng bộ lọc tài khoản
        account_filter = DebtBalanceQueryUtils.build_account_filter(account_codes)

        # Lấy danh sách khách hàng có giao dịch trong kỳ
        customer_ids = TransactionModel.objects.filter(
            journal_entry__timestamp__date__gte=start_date,
            journal_entry__timestamp__date__lte=end_date,
            journal_entry__ledger__entity_id=entity_id,
            journal_entry__ledger__posted=True,
            journal_entry__ledger__hidden=False
        ).filter(account_filter).values_list(
            'journal_entry__customer_id', flat=True
        ).distinct()

        return CustomerModel.objects.filter(
            uuid__in=customer_ids,
            entity_model_id=entity_id,
            active=True
        ).order_by('customer_code')
