# Sổ Chi Tiết Công Nợ Nhiều Khách Hàng - Tài Liệu Nghiệp Vụ ERP

## Tổng Quan Nghiệp Vụ

**Sổ Chi Tiết Công Nợ Nhiều Khách Hàng** là một báo cáo quan trọng trong hệ thống ERP, cung cấp thông tin chi tiết về tình hình công nợ của nhiều khách hàng trong một tài khoản cụ thể và khoảng thời gian nhất định.

## Mục Đích Sử Dụng

### 1. <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON> Công Nợ Khách Hàng
- **Theo dõi số dư**: <PERSON><PERSON> số dư đ<PERSON>, phát sinh trong kỳ và số dư cuối kỳ
- **Phân tích xu hướng**: Đ<PERSON>h giá xu hướng thanh toán của khách hàng
- **Kiểm soát rủi ro**: <PERSON><PERSON><PERSON> hiện sớm các khách hàng có nguy cơ nợ xấu

### 2. <PERSON><PERSON>o C<PERSON>o Tà<PERSON>
- **<PERSON><PERSON>o cáo định kỳ**: Cung cấp dữ liệu cho báo cáo tài chính hàng tháng/quý/năm
- **Kiểm toán**: Hỗ trợ công tác kiểm toán nội bộ và bên ngoài
- **Tuân thủ pháp luật**: Đáp ứng yêu cầu báo cáo theo quy định

### 3. Ra Quyết Định Kinh Doanh
- **Chính sách tín dụng**: Điều chỉnh chính sách bán hàng trả chậm
- **Quản lý dòng tiền**: Dự báo dòng tiền từ việc thu hồi công nợ
- **Đánh giá khách hàng**: Phân loại khách hàng theo mức độ tín nhiệm

## Khái Niệm Chuyên Môn

### 1. Các Loại Số Dư

#### Số Dư Đầu Kỳ (no_dk/co_dk)
- **Định nghĩa**: Số dư công nợ tại thời điểm bắt đầu kỳ báo cáo
- **Tính toán**: Tổng hợp tất cả giao dịch từ đầu đến ngày bắt đầu kỳ - 1
- **Ý nghĩa**: 
  - `no_dk` > 0: Khách hàng nợ công ty
  - `co_dk` > 0: Công ty nợ khách hàng

#### Phát Sinh Trong Kỳ (ps_no/ps_co)
- **Định nghĩa**: Các giao dịch phát sinh trong khoảng thời gian báo cáo
- **Tính toán**: 
  - `ps_no`: Tổng các giao dịch bên Nợ (khách hàng mua hàng)
  - `ps_co`: Tổng các giao dịch bên Có (khách hàng thanh toán)
- **Ý nghĩa**: Thể hiện hoạt động giao dịch trong kỳ

#### Số Dư Cuối Kỳ (no_ck/co_ck)
- **Định nghĩa**: Số dư công nợ tại thời điểm cuối kỳ báo cáo
- **Công thức**: Số dư cuối kỳ = Số dư đầu kỳ + Phát sinh Nợ - Phát sinh Có
- **Ý nghĩa**: Tình hình công nợ hiện tại cần theo dõi

### 2. Phân Loại Tài Khoản

#### Tài Khoản Phải Thu Khách Hàng (131)
- **Mục đích**: Theo dõi các khoản phải thu từ bán hàng, cung cấp dịch vụ
- **Tính chất**: Tài khoản tài sản, tăng bên Nợ, giảm bên Có
- **Ví dụ**: Bán hàng chưa thu tiền, cung cấp dịch vụ chưa thanh toán

#### Tài Khoản Phải Trả Khách Hàng (331)
- **Mục đích**: Theo dõi các khoản phải trả cho khách hàng
- **Tính chất**: Tài khoản nợ phải trả, tăng bên Có, giảm bên Nợ
- **Ví dụ**: Tiền đặt cọc của khách hàng, tiền thừa chưa trả

### 3. Bộ Lọc Nâng Cao

#### Lọc Theo Nhóm Khách Hàng
- **Nhóm KH 1 (nh_kh1)**: Phân loại theo quy mô (Lớn/Vừa/Nhỏ)
- **Nhóm KH 2 (nh_kh2)**: Phân loại theo ngành nghề
- **Nhóm KH 3 (nh_kh3)**: Phân loại theo mức độ tín nhiệm

#### Lọc Theo Khu Vực (rg_code)
- **Mục đích**: Phân tích công nợ theo vùng địa lý
- **Ứng dụng**: Đánh giá hiệu quả kinh doanh theo khu vực

## Quy Trình Nghiệp Vụ

### 1. Chuẩn Bị Dữ Liệu
```
Bước 1: Xác định kỳ báo cáo (từ ngày - đến ngày)
Bước 2: Chọn tài khoản cần theo dõi (131, 331, v.v.)
Bước 3: Thiết lập bộ lọc (nhóm KH, khu vực)
Bước 4: Chạy báo cáo
```

### 2. Phân Tích Kết Quả
```
Bước 1: Xem dòng tổng cộng (stt = 0)
Bước 2: Phân tích từng khách hàng
Bước 3: So sánh với kỳ trước
Bước 4: Xác định hành động cần thiết
```

### 3. Hành Động Theo Dõi
```
Bước 1: Liên hệ khách hàng có số dư lớn
Bước 2: Lập kế hoạch thu hồi công nợ
Bước 3: Cập nhật chính sách tín dụng
Bước 4: Báo cáo lên cấp quản lý
```

## Ví Dụ Thực Tế

### Tình Huống 1: Công Ty Bán Lẻ
**Bối cảnh**: Công ty bán lẻ cần theo dõi công nợ khách hàng tháng 6/2025

**Thiết lập báo cáo**:
- Kỳ báo cáo: 01/06/2025 - 30/06/2025
- Tài khoản: 131 (Phải thu khách hàng)
- Bộ lọc: Tất cả khách hàng

**Kết quả mẫu**:
```
Tổng cộng:
- Số dư đầu kỳ: 500,000,000 VNĐ
- Phát sinh bán hàng: 200,000,000 VNĐ
- Phát sinh thu tiền: 150,000,000 VNĐ
- Số dư cuối kỳ: 550,000,000 VNĐ

Khách hàng ABC:
- Số dư đầu kỳ: 50,000,000 VNĐ
- Mua hàng trong tháng: 20,000,000 VNĐ
- Thanh toán trong tháng: 15,000,000 VNĐ
- Số dư cuối kỳ: 55,000,000 VNĐ
```

**Phân tích**:
- Công nợ tăng 10% so với đầu tháng
- Khách hàng ABC có xu hướng thanh toán chậm
- Cần tăng cường công tác thu hồi công nợ

### Tình Huống 2: Công Ty Sản Xuất
**Bối cảnh**: Theo dõi công nợ khách hàng VIP (nhóm KH1)

**Thiết lập báo cáo**:
- Kỳ báo cáo: 01/06/2025 - 30/06/2025
- Tài khoản: 131 (Phải thu khách hàng)
- Bộ lọc: nh_kh1 = "VIP"

**Mục đích**:
- Đánh giá tình hình thanh toán của khách hàng VIP
- Điều chỉnh chính sách tín dụng cho từng nhóm
- Lập kế hoạch chăm sóc khách hàng

## Lưu Ý Quan Trọng

### 1. Tính Chính Xác Dữ Liệu
- **Đồng bộ dữ liệu**: Đảm bảo tất cả giao dịch đã được ghi nhận
- **Kiểm tra chéo**: So sánh với sổ sách kế toán
- **Xử lý lỗi**: Xác minh các số liệu bất thường

### 2. Bảo Mật Thông Tin
- **Phân quyền truy cập**: Chỉ nhân viên có thẩm quyền mới xem được
- **Lưu trữ an toàn**: Bảo vệ dữ liệu khỏi truy cập trái phép
- **Tuân thủ quy định**: Đáp ứng yêu cầu bảo mật dữ liệu

### 3. Tần Suất Báo Cáo
- **Hàng ngày**: Theo dõi khách hàng có số dư lớn
- **Hàng tuần**: Báo cáo tình hình tổng thể
- **Hàng tháng**: Phân tích xu hướng và lập kế hoạch
- **Hàng quý**: Đánh giá hiệu quả chính sách tín dụng

## Tích Hợp Với Các Module Khác

### 1. Module Bán Hàng
- **Liên kết**: Tự động cập nhật khi có đơn hàng mới
- **Kiểm soát**: Cảnh báo khi khách hàng vượt hạn mức tín dụng

### 2. Module Kế Toán
- **Đồng bộ**: Tự động đồng bộ với sổ sách kế toán
- **Báo cáo**: Cung cấp dữ liệu cho báo cáo tài chính

### 3. Module CRM
- **Thông tin khách hàng**: Kết hợp với thông tin liên hệ
- **Lịch sử giao dịch**: Theo dõi lịch sử quan hệ kinh doanh

## Kết Luận

Sổ Chi Tiết Công Nợ Nhiều Khách Hàng là công cụ quan trọng giúp doanh nghiệp:
- Quản lý hiệu quả công nợ khách hàng
- Đưa ra quyết định kinh doanh đúng đắn
- Kiểm soát rủi ro tài chính
- Tối ưu hóa dòng tiền

Việc sử dụng đúng cách báo cáo này sẽ góp phần nâng cao hiệu quả quản lý tài chính và phát triển bền vững của doanh nghiệp.
