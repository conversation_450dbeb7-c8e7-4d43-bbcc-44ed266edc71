# So Chi Tiet Cong No Nhieu Khach Hang API - Technical Documentation

## Overview

The **So Chi Tiet Cong No Nhieu Khach Hang** (Customer Debt Detail Report - Multiple Customers) API provides detailed debt transaction records for multiple customers within a specified account and date range. This API is designed for detailed debt analysis and transaction tracking.

## API Endpoint

```
POST /api/entities/{entity_slug}/erp/ban-hang/cong-no-khach-hang/so-chi-tiet-cong-no-nhieu-khach-hang/
```

## Authentication

- **Type**: Basic Authentication
- **Required**: Yes
- **Header**: `Authorization: Basic {base64_encoded_credentials}`

## Request Format

### Headers

```
Content-Type: application/json
Authorization: Basic {credentials}
```

### Request Body Schema

| Field      | Type          | Required | Description                       | Example                                |
| ---------- | ------------- | -------- | --------------------------------- | -------------------------------------- |
| `ngay_ct1` | string        | Yes      | Start date (YYYYMMDD format)      | "********"                             |
| `ngay_ct2` | string        | Yes      | End date (YYYYMMDD format)        | "********"                             |
| `tk`       | string (UUID) | Yes      | Account UUID                      | "1f678a30-5ac0-4a5e-b417-21ddfebea837" |
| `ma_kh`    | string (UUID) | No       | Specific customer UUID (optional) | "customer-uuid-here"                   |
| `nh_kh1`   | string (UUID) | No       | Customer Group 1 UUID filter      | "uuid-string"                          |
| `nh_kh2`   | string (UUID) | No       | Customer Group 2 UUID filter      | "uuid-string"                          |
| `nh_kh3`   | string (UUID) | No       | Customer Group 3 UUID filter      | "uuid-string"                          |
| `rg_code`  | string (UUID) | No       | Region UUID filter                | "uuid-string"                          |
| `ct_vt`    | integer       | No       | Product detail flag (0/1)         | 0                                      |
| `so_du`    | integer       | No       | Running balance flag (0/1)        | 0                                      |
| `tk_cn`    | integer       | No       | Account branch flag (0/1)         | 0                                      |
| `mau_bc`   | integer       | No       | Report template number            | 20                                     |

### Example Request

```json
{
  "ngay_ct1": "********",
  "ngay_ct2": "********",
  "tk": "1f678a30-5ac0-4a5e-b417-21ddfebea837",
  "ma_kh": "",
  "nh_kh1": "group1-uuid",
  "ct_vt": 0,
  "so_du": 0,
  "tk_cn": 0,
  "mau_bc": 20
}
```

## Response Format

### Success Response (200 OK)

The API returns paginated results using Django REST Framework pagination:

```json
{
  "count": 25,
  "next": "http://localhost:8003/api/.../so-chi-tiet-cong-no-nhieu-khach-hang/?page=2",
  "previous": null,
  "results": [
    {
      "stt": 0,
      "tk": "",
      "ma_kh": "customer-uuid-here",
      "ten_kh": "Tổng cộng",
      "no_dk": 5000000.0,
      "co_dk": 1000000.0,
      "ps_no": 1500000.0,
      "ps_co": 500000.0,
      "no_ck": 6000000.0,
      "co_ck": 0.0,
      "bac_ct": 0
    },
    {
      "stt": 1,
      "tk": "131",
      "ma_kh": "KH001",
      "ten_kh": "Công ty ABC",
      "no_dk": 2000000.0,
      "co_dk": 0.0,
      "ps_no": 500000.0,
      "ps_co": 0.0,
      "no_ck": 2500000.0,
      "co_ck": 0.0,
      "bac_ct": 0
    }
  ]
}
```

### Response Fields

| Field    | Type    | Description                         |
| -------- | ------- | ----------------------------------- |
| `stt`    | integer | Sequential number (0 = summary row) |
| `tk`     | string  | Account code                        |
| `ma_kh`  | string  | Customer code                       |
| `ten_kh` | string  | Customer name                       |
| `no_dk`  | float   | Opening debit balance               |
| `co_dk`  | float   | Opening credit balance              |
| `ps_no`  | float   | Period debit movements              |
| `ps_co`  | float   | Period credit movements             |
| `no_ck`  | float   | Closing debit balance               |
| `co_ck`  | float   | Closing credit balance              |
| `bac_ct` | integer | Document level                      |

### Error Responses

#### 400 Bad Request

```json
{
  "detail": "Invalid parameters",
  "errors": {
    "tk": ["Account UUID is required and cannot be empty"],
    "ngay_ct1": ["Start date must be in YYYYMMDD format"]
  }
}
```

#### 401 Unauthorized

```json
{
  "detail": "Authentication credentials were not provided."
}
```

## API Flow Architecture

### 1. Request Entry Point

```
ViewSet: SoChiTietCongNoNhieuKhachHangViewSet.get_report()
Location: django_ledger/api/views/ban_hang/cong_no_khach_hang/so_chi_tiet_cong_no_nhieu_khach_hang/
```

### 2. Request Validation

```
Serializer: SoChiTietCongNoNhieuKhachHangRequestSerializer.validate()
- Validates UUID format for tk field
- Validates date format (YYYYMMDD)
- Validates required fields
```

### 3. Business Logic Processing

```
Service: SoChiTietCongNoNhieuKhachHangService.generate_report()
Location: django_ledger/services/ban_hang/cong_no_khach_hang/so_chi_tiet_cong_no_nhieu_khach_hang/
```

### 4. Data Processing Flow

#### Step 1: Filter Parsing

```python
_parse_enhanced_filters()
- Parse date range (ngay_ct1, ngay_ct2)
- Validate account UUID (tk)
- Parse customer filters (ma_kh, nh_kh1, nh_kh2, nh_kh3, rg_code)
```

#### Step 2: Customer Filtering

```python
CustomerFilteringUtils.get_filtered_customers()
- Apply UUID-based filters (nh_kh1, nh_kh2, nh_kh3, rg_code)
- Filter by specific customer code if provided
- Return active customers only
```

#### Step 3: Account Validation

```python
AccountModel.objects.get(uuid=account_uuid)
- Validate account exists
- Get account code for display
```

#### Step 4: Debt Calculation

```python
_calculate_customer_debt_for_account()
For each customer:
  - Calculate opening balance (TYPE 1)
  - Calculate period movements (TYPE 2)
  - Calculate closing balance (TYPE 3)
  - Format customer debt record
```

#### Step 5: Summary Row Creation

```python
SummaryRowUtils.add_summary_to_report()
- Calculate totals from all customer records
- Create summary row with stt=0, ten_kh="Tổng cộng"
- Insert summary row at beginning of results
```

### 5. Response Serialization

```
Serializer: SoChiTietCongNoNhieuKhachHangResponseSerializer
- Serialize each record with proper field mapping
- Apply pagination using ERPPagination
```

## Utility Classes Used

### CustomerFilteringUtils

- **Purpose**: UUID-based customer filtering
- **Methods**:
  - `parse_uuid_filters()`: Parse UUID filter parameters
  - `apply_uuid_filters()`: Apply filters to queryset
  - `get_filtered_customers()`: Get filtered customer list

### SummaryRowUtils

- **Purpose**: Summary row creation and management
- **Methods**:
  - `calculate_totals()`: Calculate totals from data rows
  - `create_summary_row()`: Create summary row with totals
  - `add_summary_to_report()`: Add summary row to report

### DebtBalanceCalculationUtils

- **Purpose**: Core debt calculation logic
- **Methods**:
  - `calculate_opening_balance()`: Calculate opening balance
  - `calculate_period_movements()`: Calculate period movements
  - `calculate_closing_balance()`: Calculate closing balance

### DebtBalanceFormatterUtils

- **Purpose**: Format debt records for API response
- **Methods**:
  - `format_customer_debt_record()`: Format complete customer record
  - `format_balance_columns()`: Format debit/credit columns

## Example Usage

### Basic Request

```bash
curl --location 'http://localhost:8003/api/entities/my-company/erp/ban-hang/cong-no-khach-hang/so-chi-tiet-cong-no-nhieu-khach-hang/' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46cGFzc3dvcmQ=' \
--data '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "tk": "1f678a30-5ac0-4a5e-b417-21ddfebea837"
}'
```

### Filtered Request (Customer Group)

```bash
curl --location 'http://localhost:8003/api/entities/my-company/erp/ban-hang/cong-no-khach-hang/so-chi-tiet-cong-no-nhieu-khach-hang/' \
--header 'Content-Type: application/json' \
--header 'Authorization: Basic YWRtaW46cGFzc3dvcmQ=' \
--data '{
    "ngay_ct1": "********",
    "ngay_ct2": "********",
    "tk": "1f678a30-5ac0-4a5e-b417-21ddfebea837",
    "nh_kh1": "group1-uuid-here"
}'
```

## Performance Considerations

- **Pagination**: Results are paginated to handle large datasets
- **Database Optimization**: Uses select_related for efficient queries
- **Caching**: Supports Django cache framework
- **Error Handling**: Graceful error handling with detailed error messages

## Security Features

- **Authentication Required**: Basic authentication mandatory
- **Input Validation**: Comprehensive input validation
- **SQL Injection Protection**: Uses Django ORM parameterized queries
- **UUID Validation**: Strict UUID format validation
